# ================================ 导入库模块 ===================================
import os
import re
import h5py
import numpy as np
import pandas as pd
import xarray as xr
import seaborn as sns
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib import font_manager
import scipy.stats as ss
from scipy.stats import pearsonr
from scipy import stats
from PIL import Image
from tqdm import tqdm
import time
from scipy import fftpack
import scipy.integrate
from sklearn.metrics import mean_squared_error
from scipy.io import loadmat
from warnings import filterwarnings
from scipy.interpolate import interp2d
from scipy import signal
# 降维算法导入
from sklearn import decomposition
from sklearn.decomposition import PCA, FastICA, FactorAnalysis, IncrementalPCA, KernelPCA, SparsePCA
from sklearn.manifold import MDS, TSNE, LocallyLinearEmbedding, Isomap, SpectralEmbedding
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis as LDA  # 可以分类和降维
from sklearn.discriminant_analysis import QuadraticDiscriminantAnalysis as QDA  # 只能分类
from sklearn.manifold._locally_linear import barycenter_kneighbors_graph
from sklearn.metrics import euclidean_distances
from sklearn.metrics.pairwise import rbf_kernel
from sklearn.neighbors import kneighbors_graph

# 颜色映射设置
import cmasher as cmr
cmapv = cmr.get_sub_cmap('cmr.fusion_r', 0, 1, N=64)      # 速度场颜色映射
cmapp = cmr.get_sub_cmap('cmr.copper', 0, 1, N=64)       # 压力场颜色映射
cmapw = cmr.get_sub_cmap('cmr.viola', 0, 1)              # 涡量场颜色映射
cmapwater = cmr.get_sub_cmap('cmr.ocean', 0, 1)          # 水相颜色映射
cmap = cmr.get_sub_cmap('cmr.seasons', 0, 1, N=64)       # 通用颜色映射
# 备选颜色映射：'RdBu_r'(红蓝白)，'RdBu'（红蓝反向），'rainbow'(红蓝)

# 忽略警告信息
filterwarnings('ignore')

# ================================ 字体设置函数 =================================
# 设置字体优先级列表
plt.rcParams['font.family'] = ['Times New Roman', 'SimHei']
plt.rcParams['font.sans-serif'] = ['Times New Roman', 'SimHei', 'DejaVu Sans']
plt.rcParams['font.serif'] = ['Times New Roman', 'SimSun', 'DejaVu Serif']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.unicode_minus'] = False

# 设置图形质量
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = 'white'

# 设置标签和标题样式
plt.rcParams['axes.labelsize'] = 12
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['xtick.labelsize'] = 10
plt.rcParams['ytick.labelsize'] = 10
plt.rcParams['legend.fontsize'] = 10

# 设置线条和网格
plt.rcParams['lines.linewidth'] = 1.5
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.grid'] = True

# ================================================================================
# 数据集1：Re=100时2D圆柱绕流数据集 (Cylinder2D_Dataset_C)
# ================================================================================
"""
数据集描述：
- 数据来源：HFM-master/PINNData/Cylinder2D.mat
- 物理特征：雷诺数Re=100的层流圆柱绕流
- 空间维度：2D平面流动
- 时间采样：时间间隔0.08s
- 数据类型：包含浓度场、速度场、压力场的时空演化数据
- 应用场景：用于验证降维算法在层流条件下的性能
"""

# 数据文件路径
cylinder2d_data_path = "D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D.mat"
# 备选数据路径（花瓣形局部观测）
# cylinder2d_data_path = "D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_flower.mat"

# 加载Cylinder2D数据集
print("正在加载Cylinder2D数据集...")
cylinder2d_raw_data = loadmat(cylinder2d_data_path)

# 数据稀疏化参数设置
cylinder2d_sparse_factor = 1  # 稀疏化因子，1表示不稀疏化

# 提取Cylinder2D数据集的各个物理场
cylinder2d_concentration = cylinder2d_raw_data["C_star"][::cylinder2d_sparse_factor, :]  # 浓度场 C (N×T)
cylinder2d_velocity_x = cylinder2d_raw_data["U_star"][::cylinder2d_sparse_factor, :]     # X方向速度 (N×T)
cylinder2d_velocity_y = cylinder2d_raw_data["V_star"][::cylinder2d_sparse_factor, :]     # Y方向速度 (N×T)
cylinder2d_pressure = cylinder2d_raw_data["P_star"][::cylinder2d_sparse_factor, :]       # 压力场 (N×T)
cylinder2d_time = cylinder2d_raw_data["t_star"][::cylinder2d_sparse_factor, :]           # 时间序列 (T×1)
cylinder2d_coord_x = cylinder2d_raw_data["x_star"][::cylinder2d_sparse_factor, :]        # X坐标 (N×1)
cylinder2d_coord_y = cylinder2d_raw_data["y_star"][::cylinder2d_sparse_factor, :]        # Y坐标 (N×1)

# 数据集基本信息输出
print(f"Cylinder2D数据集信息：")
print(f"  - 空间点数：{cylinder2d_concentration.shape[0]}")
print(f"  - 时间步数：{cylinder2d_concentration.shape[1]}")
print(f"  - 时间范围：{cylinder2d_time.min():.3f} - {cylinder2d_time.max():.3f} s")
print(f"  - 空间范围：X[{cylinder2d_coord_x.min():.2f}, {cylinder2d_coord_x.max():.2f}], Y[{cylinder2d_coord_y.min():.2f}, {cylinder2d_coord_y.max():.2f}]")

# Cylinder2D数据集可视化 - 浓度场快照
cylinder2d_snapshot_time = 0  # 选择的快照时间步
plt.figure(figsize=(8, 4), dpi=300)
plt.tripcolor(cylinder2d_coord_x[:, cylinder2d_snapshot_time],
              cylinder2d_coord_y[:, cylinder2d_snapshot_time],
              cylinder2d_concentration[:, cylinder2d_snapshot_time],
              shading='gouraud', cmap=cmapv)
plt.axis('equal')
plt.colorbar(label='浓度 ')
plt.title(f'Cylinder2D数据集 - 浓度场快照 (t={cylinder2d_snapshot_time})')
plt.xlabel('X')
plt.ylabel('Y')
plt.show()

# ================================================================================
# 数据集2：Re=100时3D圆柱绕流数据集 (Cylinder3D_Dataset_E)
# ================================================================================
"""
数据集描述：
- 数据来源：HFM-master/PINNData/Cylinder3D.mat
- 物理特征：雷诺数Re=100的层流圆柱绕流
- 空间维度：3D立体流动
- 数据类型：包含浓度场、三维速度场、压力场的时空演化数据
- 应用场景：用于验证降维算法在三维流动中的性能
"""

# 数据文件路径
cylinder3d_data_path = "D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder3D.mat"

# 加载Cylinder3D数据集
print("正在加载Cylinder3D数据集...")
cylinder3d_raw_data = loadmat(cylinder3d_data_path)

# 提取Cylinder3D数据集的各个物理场
cylinder3d_concentration = cylinder3d_raw_data["C_star"]  # 浓度场 C (N×T)
cylinder3d_velocity_x = cylinder3d_raw_data["U_star"]     # X方向速度 (N×T)
cylinder3d_velocity_y = cylinder3d_raw_data["V_star"]     # Y方向速度 (N×T)
cylinder3d_velocity_z = cylinder3d_raw_data["W_star"]     # Z方向速度 (N×T)
cylinder3d_pressure = cylinder3d_raw_data["P_star"]       # 压力场 (N×T)
cylinder3d_time = cylinder3d_raw_data["t_star"]           # 时间序列 (T×1)
cylinder3d_coord_x = cylinder3d_raw_data["x_star"]        # X坐标 (N×1)
cylinder3d_coord_y = cylinder3d_raw_data["y_star"]        # Y坐标 (N×1)
cylinder3d_coord_z = cylinder3d_raw_data["z_star"]        # Z坐标 (N×1)

# 数据集基本信息输出
print(f"Cylinder3D数据集信息：")
print(f"  - 空间点数：{cylinder3d_concentration.shape[0]}")
print(f"  - 时间步数：{cylinder3d_concentration.shape[1]}")
print(f"  - 时间范围：{cylinder3d_time.min():.3f} - {cylinder3d_time.max():.3f} s")
print(f"  - 空间范围：X[{cylinder3d_coord_x.min():.2f}, {cylinder3d_coord_x.max():.2f}], Y[{cylinder3d_coord_y.min():.2f}, {cylinder3d_coord_y.max():.2f}], Z[{cylinder3d_coord_z.min():.2f}, {cylinder3d_coord_z.max():.2f}]")

# Cylinder3D数据集可视化 - 中跨度切面的X方向速度快照
cylinder3d_snapshot_time = 0  # 选择的快照时间步
plt.figure(figsize=(8, 4), dpi=300)
plt.tripcolor(cylinder3d_coord_x[:, cylinder3d_snapshot_time],
              cylinder3d_coord_y[:, cylinder3d_snapshot_time],
              cylinder3d_velocity_x[:, cylinder3d_snapshot_time],
              shading='gouraud', cmap=cmapv)
plt.axis('equal')
plt.colorbar(label='X方向速度 U')
plt.title(f'Cylinder3D数据集 - X方向速度场快照 (t={cylinder3d_snapshot_time})')
plt.xlabel('X坐标')
plt.ylabel('Y坐标')
plt.show()

# ================================================================================
# 数据集3：低雷诺数俯仰翼型数据集 (Pitching_Airfoil_Dataset)
# ================================================================================
"""
数据集描述：
- 数据来源：低雷诺数的俯仰翼型DNS数据
- 物理特征：雷诺数Re=100的层流俯仰翼型流动
- 非线性特征：存在系统的非线性动力学特征
- 空间维度：2D平面流动
- 数据类型：包含速度场、涡量场、升力系数、阻力系数等
- 应用场景：用于验证降维算法在非定常流动中的性能
"""
# 关闭之前的图形窗口
plt.close('all')
# 数据文件路径
pitching_airfoil_data_dir = 'D:/基准数据/低雷诺数的俯仰翼'
# 读取俯仰翼型参数文件
print("正在加载俯仰翼型参数...")
pitching_params_file = h5py.File(os.path.join(pitching_airfoil_data_dir, 'airfoilDNS_parameters.h5'), 'r')
pitching_dt_field = pitching_params_file['/dt_field'][()]      # 流场时间步长
pitching_dt_force = pitching_params_file['/dt_force'][()]      # 力时间步长
pitching_reynolds = pitching_params_file['/Re'][()]           # 雷诺数
pitching_frequencies = pitching_params_file['/frequencies'][()] # 俯仰频率
pitching_alpha_p = pitching_params_file['/alpha_p'][()]       # 俯仰幅值
pitching_alpha_0s = pitching_params_file['/alpha_0s'][()]     # 平均攻角
pitching_pitch_axis = pitching_params_file['/pitch_axis'][()]  # 俯仰轴位置
pitching_params_file.close()
# 俯仰翼型基本参数设置
pitching_base_angle = 30    # 基准角度
pitching_time_step = 5      # 时间步长参数
# 读取俯仰翼型网格文件
print("正在加载俯仰翼型网格...")
pitching_grid_file = os.path.join(pitching_airfoil_data_dir, 'airfoilDNS_grid.h5')
with h5py.File(pitching_grid_file, 'r') as gridFile:
    pitching_grid_x = gridFile['/x'][()]  # X方向网格坐标
    pitching_grid_y = gridFile['/y'][()]  # Y方向网格坐标
    pitching_nx_full = len(pitching_grid_x)  # X方向网格点数
    pitching_ny_full = len(pitching_grid_y)  # Y方向网格点数
# 数据稀疏化参数设置
pitching_sparse_factor = 2  # 稀疏化因子
# 计算稀疏化后的网格尺寸
pitching_nx = int(len(pitching_grid_x[69:569]) / pitching_sparse_factor)
pitching_ny = int(len(pitching_grid_y[75:275]) / pitching_sparse_factor)
# 提取稀疏化后的绘图坐标
pitching_plot_x = pitching_grid_x[69:569][::pitching_sparse_factor]
pitching_plot_y = pitching_grid_y[75:275][::pitching_sparse_factor]
# 数据集基本信息输出
print(f"俯仰翼型数据集信息：")
print(f"  - 雷诺数：{pitching_reynolds}")
print(f"  - 原始网格尺寸：{pitching_nx_full} × {pitching_ny_full}")
print(f"  - 稀疏化后网格尺寸：{pitching_nx} × {pitching_ny}")
print(f"  - 稀疏化因子：{pitching_sparse_factor}")
print(f"  - 基准角度：{pitching_base_angle}°")
# 俯仰频率选择参数
# 可选频率列表：['0p0', '0p05', '0p1', '0p2', '0p25', '0p3', '0p35', '0p4', '0p5']
# 对应的流动特性：
# - '0p0': 静态（非周期）
# - '0p05': 非周期流动
# - '0p1': 准周期流动
# - '0p2': 周期流动
# - '0p25': 周期流动
# - '0p3': 周期流动
# - '0p35': 周期流动
# - '0p4': 准周期流动
# - '0p5': 周期流动
pitching_freq_options = ['0p0', '0p05', '0p1', '0p2', '0p25', '0p3', '0p35', '0p4', '0p5']
pitching_freq_str = '0p2'  # 选择周期流动频率
pitching_freq_value = 0    # 对应的数值频率
# 数据重采样函数 - 每10个点取1个
def resample_every_ten_points(data_array):
    """对数据进行1/10重采样"""
    resampled_data = data_array[::10]
    return resampled_data
# 加载静态翼型数据（基准状态）
print("正在加载静态翼型数据...")
pitching_static_file = os.path.join(pitching_airfoil_data_dir, f'airfoilDNS_a30static.h5')
# 备选文件：pitching_static_file = os.path.join(pitching_airfoil_data_dir, f'airfoilDNS_a25static.h5')

with h5py.File(pitching_static_file, 'r') as dataFile:
    pitching_static_cl = dataFile['/Cl'][()]        # 升力系数
    pitching_static_cd = dataFile['/Cd'][()]        # 阻力系数
    pitching_static_alpha = dataFile['/alpha'][()]  # 攻角
    pitching_static_alphadot = dataFile['/alphadot'][()]  # 攻角变化率
    pitching_static_xa = dataFile['/xa'][()]        # 翼型X坐标
    pitching_static_ya = dataFile['/ya'][()]        # 翼型Y坐标
    pitching_static_ux = dataFile['/ux'][()]        # X方向速度
    pitching_static_uy = dataFile['/uy'][()]        # Y方向速度
    pitching_static_vort = dataFile['/vort'][()]    # 涡量场

    # 数据区域裁剪和稀疏化处理
    pitching_static_ux = pitching_static_ux[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]
    pitching_static_uy = pitching_static_uy[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]
    pitching_static_vort = pitching_static_vort[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]

    # 计算速度幅值
    pitching_static_u_magnitude = np.sqrt(pitching_static_ux**2 + pitching_static_uy**2)

    # 数据重塑为时间序列格式 (时间步数=401)
    pitching_static_u_reshaped = pitching_static_u_magnitude.reshape(401, pitching_nx * pitching_ny)
    pitching_static_ux_reshaped = pitching_static_ux.reshape(401, pitching_nx * pitching_ny)
    pitching_static_uy_reshaped = pitching_static_uy.reshape(401, pitching_nx * pitching_ny)
    pitching_static_vort_reshaped = pitching_static_vort.reshape(401, pitching_nx * pitching_ny)

    # 力系数数据重采样
    pitching_static_cl_resampled = np.array(resample_every_ten_points(pitching_static_cl.T))
    pitching_static_cd_resampled = np.array(resample_every_ten_points(pitching_static_cd.T))
    pitching_static_alpha_resampled = np.array(resample_every_ten_points(pitching_static_alpha.T))
    pitching_static_alphadot_resampled = np.array(resample_every_ten_points(pitching_static_alphadot.T))

# 加载动态俯仰翼型数据（力系数）
print("正在加载动态俯仰翼型力系数数据...")
pitching_dynamic_force_file = os.path.join(pitching_airfoil_data_dir, f'airfoilDNS_a{pitching_base_angle}f{pitching_freq_str}.h5')

with h5py.File(pitching_dynamic_force_file, 'r') as dataFile:
    pitching_dynamic_cl = dataFile['/Cl'][()]        # 动态升力系数
    pitching_dynamic_cd = dataFile['/Cd'][()]        # 动态阻力系数
    pitching_dynamic_alpha = dataFile['/alpha'][()]  # 动态攻角
    pitching_dynamic_alphadot = dataFile['/alphadot'][()]  # 动态攻角变化率

    # 力系数数据重采样
    pitching_dynamic_cl_resampled = np.array(resample_every_ten_points(pitching_dynamic_cl))
    pitching_dynamic_cd_resampled = np.array(resample_every_ten_points(pitching_dynamic_cd))
    pitching_dynamic_alpha_resampled = np.array(resample_every_ten_points(pitching_dynamic_alpha))
    pitching_dynamic_alphadot_resampled = np.array(resample_every_ten_points(pitching_dynamic_alphadot))

# 时间步数设置
# 注意：BaseAngle = 30 对应 nt = 401，BaseAngle = 25 对应 nt = 1001
pitching_time_steps = 1001  # 根据BaseAngle=25设置，如果BaseAngle=30则为401

# 加载动态俯仰翼型流场数据
print("正在加载动态俯仰翼型流场数据...")
pitching_dynamic_field_file = os.path.join(pitching_airfoil_data_dir, f'airfoilDNS_a{pitching_base_angle}f{pitching_freq_str}.h5')

with h5py.File(pitching_dynamic_field_file, 'r') as dataFile:
    pitching_dynamic_xa = dataFile['/xa'][()]        # 翼型X坐标
    pitching_dynamic_ya = dataFile['/ya'][()]        # 翼型Y坐标
    pitching_dynamic_ux = dataFile['/ux'][()]        # X方向速度场
    pitching_dynamic_uy = dataFile['/uy'][()]        # Y方向速度场
    pitching_dynamic_vort = dataFile['/vort'][()]    # 涡量场

    # 数据区域裁剪和稀疏化处理
    pitching_dynamic_ux = pitching_dynamic_ux[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]
    pitching_dynamic_uy = pitching_dynamic_uy[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]
    pitching_dynamic_vort = pitching_dynamic_vort[:, 75:275, 69:569][:, ::pitching_sparse_factor, ::pitching_sparse_factor]

    # 计算速度幅值
    pitching_dynamic_u_magnitude = np.sqrt(pitching_dynamic_ux**2 + pitching_dynamic_uy**2)

    # 数据重塑为时间序列格式
    pitching_dynamic_u_reshaped = pitching_dynamic_u_magnitude.reshape(pitching_time_steps, pitching_nx * pitching_ny)
    pitching_dynamic_ux_reshaped = pitching_dynamic_ux.reshape(pitching_time_steps, pitching_nx * pitching_ny)
    pitching_dynamic_uy_reshaped = pitching_dynamic_uy.reshape(pitching_time_steps, pitching_nx * pitching_ny)
    pitching_dynamic_vort_reshaped = pitching_dynamic_vort.reshape(pitching_time_steps, pitching_nx * pitching_ny)
# 俯仰翼型数据集可视化
print("正在生成俯仰翼型数据集可视化...")

# 选择可视化的时间步（对应动态数据的时间索引）
pitching_vis_time_step = 36

# 俯仰翼型涡量场可视化
plt.figure(figsize=(8, 3), dpi=200)
pitching_vort_snapshot = pitching_dynamic_vort[pitching_vis_time_step, :, :]
pitching_contour = plt.contourf(pitching_plot_x, pitching_plot_y, pitching_vort_snapshot,
                               levels=np.linspace(-3, 3, 32), vmin=-3, vmax=3,
                               cmap='RdBu_r', extend='both')
pitching_contour.set_clim(-3, 3)
pitching_colorbar = plt.colorbar(pitching_contour)
pitching_colorbar.set_label('涡量值')
plt.xlabel('X')
plt.ylabel('Y')
plt.title(f'俯仰翼型涡量场快照 (时间步={pitching_vis_time_step})')
plt.show()

# 数据集信息总结输出
print(f"俯仰翼型数据集加载完成：")
print(f"  - 静态数据时间步数：401")
print(f"  - 动态数据时间步数：{pitching_time_steps}")
print(f"  - 选择的俯仰频率：{pitching_freq_str}")
print(f"  - 空间网格尺寸：{pitching_nx} × {pitching_ny}")
print(f"  - 数据包含：速度场(ux, uy)、涡量场(vort)、力系数(Cl, Cd)")

# ================================================================================
# 数据集4：圆柱绕流PIV实验数据集 (Cylinder_PIV_Dataset)
# ================================================================================
"""
数据集描述：
- 数据来源：MODULO-master/Matlab_Exercises/Exercise_5/Data
- 实验条件：瞬态条件下流过直径d=5mm、长度L=20cm的圆柱体流动
- 测量方法：粒子图像测速法(PIV)实验数据
- 空间分辨率：71×30点网格，空间分辨率约为Δx=0.85mm
- 时间分辨率：采样频率3kHz，总时间步数13200
- 数据类型：包含X、Y方向速度场的时空演化数据
- 应用场景：用于验证降维算法在实验数据中的性能
"""

# 数据文件路径
piv_data_folder = 'D:/基准数据/数据库/Ex_5_TR_PIV_Cylinder'

# PIV数据集基本参数
piv_total_timesteps = 13200  # 总时间步数
piv_sampling_freq = 3000     # 采样频率 (Hz)
piv_time_interval = 1 / piv_sampling_freq  # 时间间隔
piv_time_array = np.arange(0, piv_total_timesteps) * piv_time_interval  # 时间轴

# 读取PIV网格文件
print("正在加载PIV网格数据...")
piv_mesh_file = os.path.join(piv_data_folder, 'MESH.dat')
piv_mesh_data = np.genfromtxt(piv_mesh_file)  # 导入网格数据
piv_mesh_data = piv_mesh_data[1:, :]  # 去除头部信息，仅保留数值数据

# 提取PIV网格坐标
piv_coord_x = piv_mesh_data[:, 0]  # X坐标
piv_coord_y = piv_mesh_data[:, 1]  # Y坐标

# 数据集基本信息输出
print(f"PIV数据集信息：")
print(f"  - 总时间步数：{piv_total_timesteps}")
print(f"  - 采样频率：{piv_sampling_freq} Hz")
print(f"  - 时间间隔：{piv_time_interval:.6f} s")
print(f"  - 空间点数：{len(piv_coord_x)}")
print(f"  - 空间范围：X[{piv_coord_x.min():.2f}, {piv_coord_x.max():.2f}], Y[{piv_coord_y.min():.2f}, {piv_coord_y.max():.2f}]")

# 读取PIV流场快照数据
print("正在加载PIV流场快照数据...")
piv_velocity_data = []
for time_step in range(0, piv_total_timesteps):
    piv_snapshot_filename = f'Res{time_step:05d}.dat'
    piv_snapshot_filepath = os.path.join(piv_data_folder, piv_snapshot_filename)
    if os.path.isfile(piv_snapshot_filepath):
        piv_velocity_data.append(np.genfromtxt(piv_snapshot_filepath))

# 转换为numpy数组并提取速度分量
piv_velocity_data = np.array(piv_velocity_data)
piv_velocity_x = (piv_velocity_data[:, 1:, 0]).T  # X方向速度 (空间×时间)
piv_velocity_y = (piv_velocity_data[:, 1:, 1]).T  # Y方向速度 (空间×时间)

# 计算不同时间段的平均速度场
# 时间段划分：0-4000, 4000-7000, 7000-13200
piv_period1_mean = np.mean(piv_velocity_x[:, :4000], axis=1)      # 第一时间段平均
piv_period2_mean = np.mean(piv_velocity_x[:, 4000:7000], axis=1)  # 第二时间段平均
piv_period3_mean = np.mean(piv_velocity_x[:, 7000:], axis=1)      # 第三时间段平均

print(f"PIV数据集加载完成：")
print(f"  - X方向速度数据形状：{piv_velocity_x.shape}")
print(f"  - Y方向速度数据形状：{piv_velocity_y.shape}")
print(f"  - 时间段1 (0-4000步) 平均速度计算完成")
print(f"  - 时间段2 (4000-7000步) 平均速度计算完成")
print(f"  - 时间段3 (7000-13200步) 平均速度计算完成")

# PIV数据集可视化
print("正在生成PIV数据集可视化...")

# 选择可视化的时间步（对应PIV数据的时间索引）
piv_vis_timestep = 2500

# PIV数据集瞬时速度场可视化
plt.figure(figsize=(7, 4), dpi=300)
piv_circle = plt.Circle((-0.5, 0), 5, color='black')  # 圆柱体表示
piv_contour1 = plt.tripcolor(piv_coord_x, piv_coord_y, piv_velocity_x[:, piv_vis_timestep],
                            vmin=-2.5, vmax=15, cmap=cmap, shading='gouraud')
plt.gca().add_patch(piv_circle)
plt.axis('equal')
plt.colorbar(piv_contour1, label='X方向速度')
plt.title(f'PIV数据集 - X方向速度场快照 (时间步={piv_vis_timestep})')
plt.xlabel('X')
plt.ylabel('Y')
plt.savefig('piv_instantaneous_velocity.png', dpi=300)
plt.show()

# PIV数据集第一时间段平均速度场可视化
plt.figure(figsize=(7, 4), dpi=300)
piv_circle2 = plt.Circle((-0.5, 0), 5, color='black')  # 圆柱体表示
piv_contour2 = plt.tripcolor(piv_coord_x, piv_coord_y, piv_period1_mean,
                            vmin=-2.5, vmax=15, cmap=cmap, shading='gouraud')
plt.gca().add_patch(piv_circle2)
plt.axis('equal')
plt.colorbar(piv_contour2, label='平均X方向速度')
plt.title('PIV数据集 - 第一时间段平均X方向速度场 (时间步0-4000)')
plt.xlabel('X')
plt.ylabel('Y')
plt.savefig('piv_period1_mean_velocity.png', dpi=300)
plt.show()
# ================================================================================
# 数据集5：NACA0012湍流数据集 (NACA0012_LES_Dataset)
# ================================================================================
"""
数据集描述：
- 数据来源：尾流的大涡模拟数据
- 物理特征：NACA0012翼型的湍流流动
- 数值方法：大涡模拟(LES)
- 空间维度：3D流动的中跨度切面数据
- 数据类型：包含三维速度场的时空演化数据
- 应用场景：用于验证降维算法在湍流数据中的性能
"""

# 关闭之前的图形窗口
plt.close('all')

# 数据文件路径
naca_les_data_dir = 'D:/基准数据/尾流的大涡模拟'

# 读取NACA0012 LES参数文件
print("正在加载NACA0012 LES参数...")
naca_params_file = naca_les_data_dir + '/airfoilLES_parameters.h5'
with h5py.File(naca_params_file, 'r') as f:
    naca_reynolds = f['/Re'][()]  # 雷诺数
    naca_time_step = f['/dt'][()]  # 快照之间的时间步长（传导时间单位）

# 读取NACA0012 LES网格信息
print("正在加载NACA0012 LES网格...")
naca_grid_file = naca_les_data_dir + '/airfoilLES_grid.h5'
with h5py.File(naca_grid_file, 'r') as f:
    naca_flow_x = f['/x'][()]  # 流场网格的X坐标
    naca_flow_y = f['/y'][()]  # 流场网格的Y坐标
    naca_airfoil_x = f['/xa'][()]  # 翼型网格的X坐标
    naca_airfoil_y = f['/ya'][()]  # 翼型网格的Y坐标
    naca_cell_volume = f['/w'][()]  # 流场网格的单元体积

# 读取NACA0012时间平均的中跨度流场信息
print("正在加载NACA0012时间平均流场...")
naca_mean_file = naca_les_data_dir + '/airfoilLES_mean_midspan.h5'
with h5py.File(naca_mean_file, 'r') as f:
    naca_ux_mean = f['/ux_mean'][()]  # X方向速度的时间平均值
    naca_uy_mean = f['/uy_mean'][()]  # Y方向速度的时间平均值
    naca_uz_mean = f['/uz_mean'][()]  # Z方向速度的时间平均值

# 数据集基本信息输出
print(f"NACA0012 LES数据集信息：")
print(f"  - 雷诺数：{naca_reynolds}")
print(f"  - 时间步长：{naca_time_step}")
print(f"  - 流场网格点数：{len(naca_flow_x)}")
print(f"  - 翼型网格点数：{len(naca_airfoil_x)}")
print(f"  - 空间范围：X[{naca_flow_x.min():.2f}, {naca_flow_x.max():.2f}], Y[{naca_flow_y.min():.2f}, {naca_flow_y.max():.2f}]")

# NACA0012时间平均流场可视化
print("正在生成NACA0012时间平均流场可视化...")
plt.figure(figsize=(8, 4), dpi=300)
plt.tripcolor(naca_flow_x, naca_flow_y, naca_ux_mean, shading='gouraud', cmap=cmapv)
plt.fill(naca_airfoil_x, naca_airfoil_y, color="white")  # 翼型填充为白色
plt.plot(naca_airfoil_x, naca_airfoil_y, color='black', linewidth=2.0)  # 翼型边界
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])  # 设置显示范围
plt.colorbar(label='X方向平均速度')
plt.title('NACA0012 LES数据集 - X方向时间平均速度场')
plt.xlabel('X')
plt.ylabel('Y')
plt.show()

# 读取NACA0012流场快照数据
print("正在加载NACA0012流场快照数据...")
naca_snapshot_range = range(5000, 10000)  # 选择时间步范围
naca_snapshot_counter = 0
naca_ux_snapshots = []
naca_uy_snapshots = []
naca_uz_snapshots = []

# 读取中跨度的快照数据
for naca_time_step in naca_snapshot_range:  # 使用独立的时间变量
    naca_snapshot_counter += 1
    naca_snapshot_file = naca_les_data_dir + '/airfoilLES_midspan/airfoilLES_t{:05d}.h5'.format(naca_time_step)
    with h5py.File(naca_snapshot_file, 'r') as f:
        naca_ux_snapshot = f['/ux'][()]  # X方向速度快照
        naca_ux_snapshots.append(naca_ux_snapshot)
        naca_uy_snapshot = f['/uy'][()]  # Y方向速度快照
        naca_uy_snapshots.append(naca_uy_snapshot)
        naca_uz_snapshot = f['/uz'][()]  # Z方向速度快照
        naca_uz_snapshots.append(naca_uz_snapshot)

# 转换为numpy数组格式 (空间×时间)
naca_ux_snapshots = (np.array(naca_ux_snapshots)).T  # N×T格式
naca_uy_snapshots = (np.array(naca_uy_snapshots)).T  # N×T格式
naca_uz_snapshots = (np.array(naca_uz_snapshots)).T  # N×T格式

# 选择可视化的时间步（对应快照数组的索引）
naca_vis_timestep_index = 10  # 快照数组中的索引

# NACA0012瞬时Z方向速度场可视化
plt.figure(figsize=(8, 4), dpi=300)
plt.tripcolor(naca_flow_x, naca_flow_y, naca_uz_snapshots[:, naca_vis_timestep_index],
              shading='gouraud', cmap=cmapv)
plt.fill(naca_airfoil_x, naca_airfoil_y, color="white")  # 翼型填充为白色
plt.plot(naca_airfoil_x, naca_airfoil_y, color='black', linewidth=2.0)  # 翼型边界
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])  # 设置显示范围
plt.colorbar(label='Z方向速度')
plt.title(f'NACA0012 LES数据集 - Z方向速度场快照 (快照索引={naca_vis_timestep_index})')
plt.xlabel('X')
plt.ylabel('Y')
plt.show()

# NACA0012速度脉动场可视化（瞬时值减去时间平均值）
plt.figure(figsize=(8, 4), dpi=300)
naca_velocity_fluctuation = naca_uz_snapshots[:, naca_vis_timestep_index] - naca_ux_mean
plt.tripcolor(naca_flow_x, naca_flow_y, naca_velocity_fluctuation,
              shading='gouraud', cmap=cmapv)
plt.fill(naca_airfoil_x, naca_airfoil_y, color="white")  # 翼型填充为白色
plt.plot(naca_airfoil_x, naca_airfoil_y, color='black', linewidth=2.0)  # 翼型边界
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])  # 设置显示范围
plt.colorbar(label='速度脉动')
plt.title(f'NACA0012 LES数据集 - 速度脉动场 (快照索引={naca_vis_timestep_index})')
plt.xlabel('X')
plt.ylabel('Y')
plt.show()

# 数据集信息总结输出
print(f"NACA0012 LES数据集加载完成：")
print(f"  - 快照数据时间范围：{min(naca_snapshot_range)} - {max(naca_snapshot_range)}")
print(f"  - 快照数据数量：{len(naca_snapshot_range)}")
print(f"  - X方向速度快照形状：{naca_ux_snapshots.shape}")
print(f"  - Y方向速度快照形状：{naca_uy_snapshots.shape}")
print(f"  - Z方向速度快照形状：{naca_uz_snapshots.shape}")

# ================================================================================
# 数据集6：空化数据集 (Cavitation_Dataset)
# ================================================================================
# =========================================空化数据集的导入==============================
from Extract_Data_RANS2022 import Data_class#原始数据位置已调整#887
#from Extract_Data_RANS2023 import Data_class#原始数据位置已调整#1866
#from Extract_Data_DES import Data_class#原始数据位置已调整#1093
#from Extract_Data_LES import Data_class#原始数据位置已调整#819
time_num  = 887#时间数887，LES819
row_num   = 125#行数
num_ppr   = 500#列数
Reduction = 0#列数减少

#2.数据类型
fieldV = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='速度')
#fieldVX = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='X速度')
#fieldVY = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Y速度')
#fieldVZ = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Z速度')
fieldW = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='涡量')
#fieldWX = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='X涡量')
#fieldWY = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Y涡量')
#fieldWZ = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Z涡量')
fieldP = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='压力')
#fieldD = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='密度')
fieldwater = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='水体积分数')
#fieldvapor = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='水蒸汽体积分数')

#3.数据定义
#3.1原始数据1：数据平铺*时间
V = fieldV.extract_data()[:,:time_num]
P = fieldP.extract_data()[:,:time_num]
W = fieldW.extract_data()[:,:time_num]
Water = fieldwater.extract_data()[:,:time_num]

#3.2原始数据2：数据：X*Y*时间
#3.3原始数据3：数据：X*Y*时间(数据选择)RANS:0-221,一个周期,121-296 296-475
#52-270 270-431
time_num1=188#105
time_num2=823#431
num=time_num2-time_num1#选取的数据个数

# 数据稀疏化
sparse_value=1
V2=np.array(V).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
P2=np.array(P).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
W2=np.array(W).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
Water2=np.array(Water).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]

# 创建坐标网格
cavitation_x_points = 500  # X方向点数
cavitation_y_points = 125  # Y方向点数
cavitation_resolution = 1  # 分辨率1mm

# 生成坐标轴
cavitation_x = np.linspace(0, 500, cavitation_x_points)  # X轴：0-500
cavitation_y = np.linspace(0, 125, cavitation_y_points)  # Y轴：0-125

# 创建网格坐标
cavitation_X, cavitation_Y = np.meshgrid(cavitation_x, cavitation_y)

# 空化数据集可视化
print("正在生成空化数据集可视化...")
cavitation_vis_timestep = 550  # 选择可视化的时间步
# 空化数据集可视化 - 水体积分数场快照
plt.figure(figsize=(8, 4), dpi=300)
cavitation_water_snapshot = Water2[:, :, cavitation_vis_timestep]
cf_water = plt.contourf(cavitation_X, cavitation_Y, cavitation_water_snapshot,
                        levels=50, cmap=cmapwater, vmin=0, vmax=1)
plt.colorbar(cf_water, label='水体积分数')
# 获取当前坐标轴并反转Y轴
ax = plt.gca()
ax.invert_yaxis()
# 填充模型几何形状（空化器模型）
power_y = np.array([69, 70, 71, 72, 73, 74, 75, 76, 77, 78,
                    79, 80, 81, 82, 83, 84, 85, 86, 87, 88,
                    89, 90,
                    90, 89, 88, 87, 86, 85, 84, 83, 82, 81,
                    80, 79, 78, 77, 76, 75, 74, 73, 72, 71,
                    70, 69, 69])
power_x = np.array([89, 81, 77, 74, 73, 72, 72, 73, 76, 79,
                    83, 87, 92, 98, 104, 111, 119, 127, 136, 147,
                    162, 189,
                    220, 217, 214, 212, 209, 206, 204, 201, 198, 195,
                    192, 189, 185, 181, 177, 173, 168, 163, 157, 150,
                    142, 131, 89])
# 绘制空化器模型
plt.fill(power_x, power_y, color="white", edgecolor='black', linewidth=2.0, alpha=0.9)
plt.plot(power_x, power_y, color='black', linewidth=3.0)
plt.title(f'空化数据集 - 水体积分数场快照 (时间步={cavitation_vis_timestep})')
plt.xlabel('X (mm)')
plt.ylabel('Y (mm)')
plt.axis('equal')
plt.tight_layout()
plt.show()

# 空化数据集可视化 - 速度场快照
plt.figure(figsize=(8, 4), dpi=300)
cavitation_velocity_snapshot = V2[:, :, cavitation_vis_timestep]
cf_velocity = plt.contourf(cavitation_X, cavitation_Y, cavitation_velocity_snapshot,
                          levels=50, cmap=cmapv, vmin=0, vmax=cavitation_velocity_snapshot.max())
plt.colorbar(cf_velocity, label='速度幅值')

# 获取当前坐标轴并反转Y轴
ax = plt.gca()
ax.invert_yaxis()

# 绘制空化器模型
plt.fill(power_x, power_y, color="white", edgecolor='black', linewidth=2.0, alpha=0.9)
plt.plot(power_x, power_y, color='black', linewidth=3.0)

plt.title(f'空化数据集 - 速度场快照 (时间步={cavitation_vis_timestep})')
plt.xlabel('X (mm)')
plt.ylabel('Y (mm)')
plt.axis('equal')
plt.tight_layout()
plt.show()

# 空化数据集可视化 - 压力场快照
plt.figure(figsize=(8, 4), dpi=300)
cavitation_pressure_snapshot = P2[:, :, cavitation_vis_timestep]
cf_pressure = plt.contourf(cavitation_X, cavitation_Y, cavitation_pressure_snapshot,
                          levels=50, cmap=cmapp,
                          vmin=cavitation_pressure_snapshot.min(),
                          vmax=cavitation_pressure_snapshot.max())
plt.colorbar(cf_pressure, label='压力')

# 获取当前坐标轴并反转Y轴
ax = plt.gca()
ax.invert_yaxis()
# 绘制空化器模型
plt.fill(power_x, power_y, color="white", edgecolor='black', linewidth=2.0, alpha=0.9)
plt.plot(power_x, power_y, color='black', linewidth=3.0)
plt.title(f'空化数据集 - 压力场快照 (时间步={cavitation_vis_timestep})')
plt.xlabel('X (mm)')
plt.ylabel('Y (mm)')
plt.axis('equal')
plt.tight_layout()
plt.show()
# 空化数据集可视化 - 涡量场快照
plt.figure(figsize=(8, 4), dpi=300)
cavitation_vorticity_snapshot = W2[:, :, cavitation_vis_timestep]
cf_vorticity = plt.contourf(cavitation_X, cavitation_Y, cavitation_vorticity_snapshot,
                           levels=50, cmap=cmapw, vmin=-5, vmax=5)
plt.colorbar(cf_vorticity, label='涡量')
# 获取当前坐标轴并反转Y轴
ax = plt.gca()
ax.invert_yaxis()
# 绘制空化器模型
plt.fill(power_x, power_y, color="white", edgecolor='black', linewidth=2.0, alpha=0.9)
plt.plot(power_x, power_y, color='black', linewidth=3.0)
plt.title(f'空化数据集 - 涡量场快照 (时间步={cavitation_vis_timestep})')
plt.xlabel('X (mm)')
plt.ylabel('Y (mm)')
plt.axis('equal')
plt.tight_layout()
plt.show()
# ================================================================================
# 数据集7：圆柱体入水数据集 (Cylinder_Water_Entry_Dataset)
# ================================================================================
"""
数据集描述：
- 数据来源：D:/基准数据/圆柱体入水/流场数据
- 物理特征：圆柱体入水过程的流场演化
- 数据格式：Excel文件，包含DMD分析结果
- 数据类型：包含水体积分数、压力场、速度场、涡量场的时空演化数据
- 应用场景：用于验证降维算法在入水冲击流动中的性能
"""

# # 数据文件路径
# cylinder_entry_base_dir = 'D:/基准数据/圆柱体入水/流场数据'
# # 圆柱体入水数据集简化加载
# import glob
# import re
# print("正在加载圆柱体入水数据集...")
# # 圆柱体入水数据集 - 最简化加载
# print("加载圆柱体入水数据集...")
# # 生成网格坐标
# # X坐标：-0.4到1.2，320个点
# x_1d = np.linspace(-0.65, 0.65, 261)
# # Z坐标：-0.65到0.65，261个点
# z_1d = np.linspace(-0.4, 1.2, 321)

# # 生成网格
# Z_grid, X_grid = np.meshgrid(z_1d, x_1d)

# # 展平为一维数组，得到所有节点的坐标
# x_coords = X_grid.flatten()  # 83781个节点的X坐标
# z_coords = Z_grid.flatten()  # 83781个节点的Z坐标

# # 简化的数据加载函数（按第一列排序）
# def load_field(field_name):
#     files = sorted(glob.glob(os.path.join(cylinder_entry_base_dir, field_name, f"{field_name}_image_*.csv")))
#     data_list = []
#     for f in files:
#         data = pd.read_csv(f)
#         # 按第一列排序，然后取第二列作为结果
#         sorted_data = data.sort_values(by=data.columns[0])
#         values = np.array(sorted_data)[:, 1] # 排序后的第二列
#         data_list.append(values)
#     return np.array(data_list)

# # 直接加载为矩阵
# cylinder_entry_data_water = load_field('DMD-water')
# cylinder_entry_data_pressure = load_field('DMD-pressure')
# cylinder_entry_data_V = load_field('DMD-V')
# cylinder_entry_data_W = load_field('DMD-W')

# # 时间坐标
# time_coords = np.arange(1, cylinder_entry_data_water.shape[0] + 1) * 0.001

# # 选择可视化的时间步索引
# cylinder_vis_timestep_index =300  # 第一个时间步

# # NACA0012瞬时Z方向速度场可视化
# plt.figure(figsize=(4, 8), dpi=300)
# plt.tripcolor(x_coords, z_coords, cylinder_entry_data_W[cylinder_vis_timestep_index, :],
#               shading='gouraud', cmap=cmapv)
# plt.show()

##########################################降维方法的应用与评价##########################

# ================================ 圆柱绕流2D数据集降维评价 ===================================
"""
基于圆柱绕流2D数据集的降维方法评价
- 数据集：cylinder_2d_data_u, cylinder_2d_data_v, cylinder_2d_data_p
- 评价方法：PCA, FastICA, NMF, t-SNE等
- 评价指标：重构误差、结构保持性、聚类性能等
- 特殊处理：无逆变换方法（t-SNE等）重构误差设为10000
"""

print("=" * 80)
print("开始圆柱绕流2D数据集降维方法评价")
print("=" * 80)

# 选择评价数据（使用圆柱绕流2D的X方向速度场）
evaluation_data = cylinder2d_velocity_x.T  # 转置为 (时间×空间) 格式
print(f"评价数据形状: {evaluation_data.shape} (时间×空间)")
print(f"数据范围: [{evaluation_data.min():.6f}, {evaluation_data.max():.6f}]")

# 数据标准化
from sklearn.preprocessing import StandardScaler
scaler = StandardScaler()
X_scaled = scaler.fit_transform(evaluation_data)
print(f"标准化后数据范围: [{X_scaled.min():.4f}, {X_scaled.max():.4f}]")
