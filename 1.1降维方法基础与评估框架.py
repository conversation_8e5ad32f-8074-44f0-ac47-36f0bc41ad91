"""
降维方法基础与评估框架
====================

本文件包含流体力学数据的降维分析方法，采用模块化设计，主要功能包括：

📁 文件结构：
├── 导入库和全局配置 (第12-106行)
├── 模块化函数部分 (第107-1122行)
│   ├── 圆柱绕流数据模块 (第107-333行)
│   ├── 翼型DNS数据模块 (第334-576行)
│   ├── 粒子数据和PIV数据模块 (第577-845行)
│   └── 降维方法模块 (第846-1122行)
├── 原始代码部分 (第1123-1720行) - 保留所有原始注释
├── 使用示例和主程序 (第1721-2520行)

🔧 主要功能：
1. 数据导入和预处理
   - 圆柱绕流数据（稳态/非稳态）
   - 翼型DNS数据（静态/动态）
   - PIV实验数据
   - 粒子数据
2. 多种降维方法的实现
   - PCA (主成分分析)
   - ICA (独立成分分析)
   - 其他方法（在原始代码中）
3. 降维效果评估和比较
4. 可视化展示

🚀 使用方法：
1. 直接运行脚本：python 1.1降维方法基础与评估框架.py
2. 选择要分析的数据类型
3. 查看分析结果和可视化

💡 设计特点：
- 保留所有原始注释内容（包括临时注释）
- 模块化函数设计，易于使用和扩展
- 详细的文档字符串和注释
- 支持多种数据格式和分析方法

作者：[您的姓名]
日期：2024年
版本：模块化重构版本
"""

# =============================================================================
# 导入必要的库
# =============================================================================

# 基础数据处理库
import os
import h5py
import numpy as np
import pandas as pd
import xarray as xr
from warnings import filterwarnings

# 可视化库
import seaborn as sns
import matplotlib.pyplot as plt
from PIL import Image
import cmasher as cmr

# 科学计算库
import scipy.stats as ss
from scipy.stats import pearsonr
from scipy import stats, signal, fftpack
import scipy.integrate
from scipy.io import loadmat
from scipy.interpolate import interp2d

# 机器学习和降维库
from sklearn.metrics import mean_squared_error, euclidean_distances
from sklearn import decomposition
from sklearn.decomposition import PCA, FastICA, FactorAnalysis, IncrementalPCA, KernelPCA, SparsePCA
from sklearn.manifold import MDS, TSNE, LocallyLinearEmbedding, Isomap, SpectralEmbedding
from sklearn.manifold._locally_linear import barycenter_kneighbors_graph
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis as LDA  # 可以分类和降维
from sklearn.discriminant_analysis import QuadraticDiscriminantAnalysis as QDA  # 只能分类
from sklearn.metrics.pairwise import rbf_kernel
from sklearn.neighbors import kneighbors_graph

# 进度条和时间处理
from tqdm import tqdm
import time

# 动态模态分解库（临时注释）
#import pydmd

# 忽略警告信息
filterwarnings('ignore')  # 忽略警告

# =============================================================================
# 全局配置和工具函数
# =============================================================================

def setup_colormaps():
    """
    设置全局颜色映射配置

    Returns:
        dict: 包含各种用途的颜色映射字典
    """
    colormaps = {
        'velocity': cmr.get_sub_cmap('cmr.fusion_r', 0, 1, N=32),  # 速度场颜色映射
        'pressure': cmr.get_sub_cmap('cmr.copper', 0, 1),         # 压力场颜色映射
        'vorticity': cmr.get_sub_cmap('cmr.viola', 0, 1),         # 涡量场颜色映射
        'water': cmr.get_sub_cmap('cmr.ocean', 0, 1),             # 水相关颜色映射
        'general': cmr.get_sub_cmap('cmr.seasons', 0, 1, N=32)    # 通用颜色映射
    }
    return colormaps

def smooth(a, nx, ny):
    """
    对二维数据进行平滑处理

    Parameters:
        a (np.ndarray): 输入的二维数据数组
        nx (int): x方向的平滑窗口大小
        ny (int): y方向的平滑窗口大小

    Returns:
        np.ndarray: 平滑处理后的数据
    """
    # 创建平滑核
    kernel = np.ones((nx, ny)) / (nx * ny)
    # 进行二维卷积平滑
    smoothed_result = signal.convolve2d(a, kernel, mode='same')
    return smoothed_result

# 初始化全局颜色映射
COLORMAPS = setup_colormaps()
cmapv = COLORMAPS['velocity']      # 速度场颜色映射
cmapp = COLORMAPS['pressure']      # 压力场颜色映射
cmapw = COLORMAPS['vorticity']     # 涡量场颜色映射
cmapwater = COLORMAPS['water']     # 水相关颜色映射
cmap = COLORMAPS['general']        # 通用颜色映射

# =============================================================================
# 数据导入模块 - 圆柱绕流数据集A
# =============================================================================

def load_cylinder_flow_data_A():
    """
    加载Re=100时圆柱绕流数据集A

    数据说明：
    - 第一行为x坐标，第二行为y坐标，第三行为密度
    - 第四行为速度u，第五行为速度v，第六行为压力，第七行为涡量

    Returns:
        dict: 包含流场数据的字典
    """
    print("正在加载圆柱绕流数据集A...")

    # 数据路径配置
    folder_path_steady = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/circular_cylinder_0112'  # 稳态
    folder_path_unsteady = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/cylinder_data'      # 非稳态

    # 选择数据路径（当前使用非稳态数据）
    folder_path = folder_path_unsteady

    # 获取所有流场快照文件
    file_list = [file for file in os.listdir(folder_path) if file.startswith("plt_") and file.endswith(".dat")]

    # 读取所有快照数据
    data_cylinder_snapshot_A = []
    for file_name in file_list:
        file_path = os.path.join(folder_path, file_name)
        file_data = np.loadtxt(file_path, skiprows=2)  # 跳过前两行标题
        data_cylinder_snapshot_A.append(file_data)

    # 第一行为x坐标，第二行为y坐标，第三行为密度，第四行为速度u，第五行为速度v，第六行为压力，第七行为涡量。
    data_cylinder_snapshot_A = np.array(data_cylinder_snapshot_A)

    print(f"成功加载 {len(data_cylinder_snapshot_A)} 个流场快照")
    return {
        'raw_data': data_cylinder_snapshot_A,
        'folder_path': folder_path,
        'data_description': '第一行为x坐标，第二行为y坐标，第三行为密度，第四行为速度u，第五行为速度v，第六行为压力，第七行为涡量'
    }

def process_cylinder_flow_data_A(raw_data, sparse=4):
    """
    处理圆柱绕流数据集A，包括网格生成和数据稀疏化

    Parameters:
        raw_data (np.ndarray): 原始流场数据
        sparse (int): 稀疏化因子，默认为4

    Returns:
        dict: 处理后的流场数据
    """
    print("正在处理圆柱绕流数据...")

    # 网格布置参数
    nx = int(((34.98-0.02)/0.02)+2)  # x方向网格点数
    ny = int(((29.98-0.02)/0.02)+1)  # y方向网格点数
    print(f"原始网格尺寸: {nx} x {ny} = {nx*ny} 个点")

    # 稀疏化策略
    sparse_factor = sparse

    # 数据定义 - 选择感兴趣区域并进行稀疏化
    X_cylinder_A = raw_data[1,:,0].reshape(ny,nx)[549:949,449:1349][::sparse_factor, ::sparse_factor]
    Y_cylinder_A = raw_data[1,:,1].reshape(ny,nx)[549:949,449:1349][::sparse_factor, ::sparse_factor]
    U_cylinder_A = raw_data[:,:,3].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse_factor, ::sparse_factor]
    V_cylinder_A = raw_data[:,:,4].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse_factor, ::sparse_factor]
    P_cylinder_A = raw_data[:,:,5].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse_factor, ::sparse_factor]
    W_cylinder_A = raw_data[:,:,6].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse_factor, ::sparse_factor]

    print(f"稀疏化后网格尺寸: {X_cylinder_A.shape}")
    print(f"时间步数: {U_cylinder_A.shape[0]}")

    return {
        'X': X_cylinder_A,      # x坐标网格
        'Y': Y_cylinder_A,      # y坐标网格
        'U': U_cylinder_A,      # x方向速度
        'V': V_cylinder_A,      # y方向速度
        'P': P_cylinder_A,      # 压力
        'W': W_cylinder_A,      # 涡量
        'sparse_factor': sparse_factor,
        'grid_info': {
            'original_nx': nx,
            'original_ny': ny,
            'processed_shape': X_cylinder_A.shape
        }
    }

def visualize_cylinder_flow_snapshot(flow_data, time_index=0, field='W'):
    """
    流场快照可视化

    Parameters:
        flow_data (dict): 流场数据字典
        time_index (int): 时间步索引，默认为0
        field (str): 要可视化的场变量，默认为'W'（涡量）
    """
    print(f"正在可视化第 {time_index} 个时间步的 {field} 场...")

    # 提取坐标网格
    X_cylinder_A = flow_data['X']
    Y_cylinder_A = flow_data['Y']

    # 选择要可视化的场变量
    if field == 'W':
        z_cylinder_A = flow_data['W'][time_index, :]
        field_name = '涡量'
    elif field == 'U':
        z_cylinder_A = flow_data['U'][time_index, :]
        field_name = 'x方向速度'
    elif field == 'V':
        z_cylinder_A = flow_data['V'][time_index, :]
        field_name = 'y方向速度'
    elif field == 'P':
        z_cylinder_A = flow_data['P'][time_index, :]
        field_name = '压力'
    else:
        z_cylinder_A = flow_data['W'][time_index, :]
        field_name = '涡量'

    # 流场快照可视化
    plt.figure(figsize=(40, 16), dpi=300)
    clev = np.arange(-0.4, 0.45, 0.1)  # 等高线级别
    cf = plt.contourf(X_cylinder_A, Y_cylinder_A, z_cylinder_A, clev, cmap=cmap, extend='both')
    plt.xlim(X_cylinder_A.min(), X_cylinder_A.max())
    plt.ylim(Y_cylinder_A.min(), Y_cylinder_A.max())

    # 添加颜色条和标签
    cbar = plt.colorbar(cf)
    cbar.set_label(f'{field_name}值')
    plt.xlabel('X坐标')
    plt.ylabel('Y坐标')
    plt.title(f'圆柱绕流{field_name}场快照 - 时间步{time_index}')
    plt.show()

def load_cylinder_force_data_A():
    """
    力的提取 - 加载圆柱绕流的力系数数据

    包含两种数据源的力系数：
    1. 稳态数据 (sparse=6)
    2. 非稳态数据 (sparse=50)

    Returns:
        dict: 包含两种力系数数据的字典
    """
    print("正在加载圆柱绕流力系数数据...")

    # 第一组力系数数据 - 稳态
    sparse_1 = 6  # 稀疏化因子
    file_path_1 = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/circular_cylinder_0112/drag.dat'
    # 第一行为计算步数，换算成时间为"步数*0.02*0.1"，第二行为阻力系数，第三行为升力系数。
    data_cylinder_force_A_1 = np.loadtxt(file_path_1, skiprows=1)
    time_cylinder_A_1 = data_cylinder_force_A_1[5049:, 0][::sparse_1] * 0.02 * 0.1
    cl_cylinder_A_1 = data_cylinder_force_A_1[5049:, 1][::sparse_1]
    cd_cylinder_A_1 = data_cylinder_force_A_1[5049:, 2][::sparse_1]

    # 第二组力系数数据 - 非稳态
    sparse_2 = 50  # 稀疏化因子
    file_path_2 = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/cylinder_data/drag.dat'
    # 第一行为计算步数，换算成时间为"步数*0.02*0.1"，第二行为阻力系数，第三行为升力系数。
    data_cylinder_force_A_2 = np.loadtxt(file_path_2, skiprows=1)
    time_cylinder_A_2 = data_cylinder_force_A_2[49:, 0][::sparse_2] * 0.02 * 0.1
    cl_cylinder_A_2 = data_cylinder_force_A_2[49:, 1][::sparse_2]
    cd_cylinder_A_2 = data_cylinder_force_A_2[49:, 2][::sparse_2]

    print(f"成功加载稳态力系数数据: {len(time_cylinder_A_1)} 个时间点")
    print(f"成功加载非稳态力系数数据: {len(time_cylinder_A_2)} 个时间点")

    return {
        'steady': {
            'time': time_cylinder_A_1,
            'lift_coefficient': cl_cylinder_A_1,
            'drag_coefficient': cd_cylinder_A_1,
            'sparse_factor': sparse_1,
            'file_path': file_path_1
        },
        'unsteady': {
            'time': time_cylinder_A_2,
            'lift_coefficient': cl_cylinder_A_2,
            'drag_coefficient': cd_cylinder_A_2,
            'sparse_factor': sparse_2,
            'file_path': file_path_2
        },
        'data_description': '第一行为计算步数，第二行为阻力系数，第三行为升力系数'
    }

def visualize_force_coefficients(force_data):
    """
    力系数可视化

    Parameters:
        force_data (dict): 力系数数据字典
    """
    print("正在可视化力系数...")

    # 创建子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # 稳态力系数可视化
    ax1.plot(force_data['steady']['time'], force_data['steady']['lift_coefficient'],
             label='升力系数 (Cl)', linewidth=2)
    ax1.plot(force_data['steady']['time'], force_data['steady']['drag_coefficient'],
             label='阻力系数 (Cd)', linewidth=2)
    ax1.set_title('稳态圆柱绕流力系数')
    ax1.set_xlabel('时间')
    ax1.set_ylabel('力系数')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 非稳态力系数可视化
    ax2.plot(force_data['unsteady']['time'], force_data['unsteady']['lift_coefficient'],
             label='升力系数 (Cl)', linewidth=2)
    ax2.plot(force_data['unsteady']['time'], force_data['unsteady']['drag_coefficient'],
             label='阻力系数 (Cd)', linewidth=2)
    ax2.set_title('非稳态圆柱绕流力系数')
    ax2.set_xlabel('时间')
    ax2.set_ylabel('力系数')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

# =============================================================================
# 数据导入模块 - 翼型DNS数据
# =============================================================================

def sample_every_ten(numbers):
    """
    数据重采样函数 - 每10个数据点采样一次

    Parameters:
        numbers (np.ndarray): 输入数据序列

    Returns:
        np.ndarray: 采样后的数据序列
    """
    return numbers[::10]

def load_airfoil_dns_data(base_angle=30, freq_str='0p2', sparse_value=2):
    """
    加载低雷诺数俯仰翼型DNS数据

    Parameters:
        base_angle (int): 基础攻角，默认30度
        freq_str (str): 频率字符串，默认'0p2'
        sparse_value (int): 数据稀疏化因子，默认2

    Returns:
        dict: 包含翼型DNS数据的字典
    """
    print(f"正在加载翼型DNS数据 - 攻角: {base_angle}°, 频率: {freq_str}")

    # 数据路径
    DIR = 'D:/基准数据/低雷诺数的俯仰翼'

    try:
        # 加载参数文件
        params_file = h5py.File(os.path.join(DIR, 'airfoilDNS_parameters.h5'), 'r')
        parameters = {
            'dt_field': params_file['/dt_field'][()],
            'dt_force': params_file['/dt_force'][()],
            'reynolds': params_file['/Re'][()],
            'frequencies': params_file['/frequencies'][()],
            'alpha_p': params_file['/alpha_p'][()],
            'alpha_0s': params_file['/alpha_0s'][()],
            'pitch_axis': params_file['/pitch_axis'][()]
        }
        params_file.close()

        # 加载网格文件
        grid_filename = os.path.join(DIR, 'airfoilDNS_grid.h5')
        with h5py.File(grid_filename, 'r') as grid_file:
            x = grid_file['/x'][()]
            y = grid_file['/y'][()]
            nx = len(x)
            ny = len(y)

        # 计算稀疏化后的网格尺寸
        nx_sparse = int(len(x[69:569])/sparse_value)
        ny_sparse = int(len(y[75:275])/sparse_value)
        plt_x = x[69:569][::sparse_value]
        plt_y = y[75:275][::sparse_value]

        print(f"原始网格: {nx} x {ny}")
        print(f"稀疏化后网格: {nx_sparse} x {ny_sparse}")

        # 可用频率选项
        freq_options = ['0p0', '0p05', '0p1', '0p2', '0p25', '0p3', '0p35', '0p4', '0p5']

        return {
            'parameters': parameters,
            'grid': {
                'x_full': x,
                'y_full': y,
                'x_sparse': plt_x,
                'y_sparse': plt_y,
                'nx': nx,
                'ny': ny,
                'nx_sparse': nx_sparse,
                'ny_sparse': ny_sparse
            },
            'config': {
                'base_angle': base_angle,
                'frequency_str': freq_str,
                'sparse_value': sparse_value,
                'available_frequencies': freq_options,
                'data_dir': DIR
            }
        }

    except Exception as e:
        print(f"加载翼型DNS数据配置时出错: {e}")
        return None

def load_airfoil_flow_data(config):
    """
    加载翼型流场数据（静态和动态）

    Parameters:
        config (dict): 翼型数据配置

    Returns:
        dict: 包含流场数据的字典
    """
    print("正在加载翼型流场数据...")

    try:
        DIR = config['config']['data_dir']
        base_angle = config['config']['base_angle']
        freq_str = config['config']['frequency_str']
        sparse_value = config['config']['sparse_value']
        nx = config['grid']['nx_sparse']
        ny = config['grid']['ny_sparse']

        # 加载静态数据
        static_filename = os.path.join(DIR, f'airfoilDNS_a{base_angle}static.h5')
        with h5py.File(static_filename, 'r') as data_file:
            static_data = {
                'lift_coeff': data_file['/Cl'][()],
                'drag_coeff': data_file['/Cd'][()],
                'angle_of_attack': data_file['/alpha'][()],
                'angle_rate': data_file['/alphadot'][()],
                'airfoil_x': data_file['/xa'][()],
                'airfoil_y': data_file['/ya'][()],
                'velocity_x': data_file['/ux']()[:, 75:275, 69:569][:, ::sparse_value, ::sparse_value],
                'velocity_y': data_file['/uy']()[:, 75:275, 69:569][:, ::sparse_value, ::sparse_value],
                'vorticity': data_file['/vort']()[:, 75:275, 69:569][:, ::sparse_value, ::sparse_value]
            }

        # 处理静态数据
        u_magnitude_static = np.sqrt(static_data['velocity_x']**2 + static_data['velocity_y']**2)
        static_data['velocity_magnitude'] = u_magnitude_static
        static_data['velocity_magnitude_reshaped'] = u_magnitude_static.reshape(401, nx * ny)
        static_data['velocity_x_reshaped'] = static_data['velocity_x'].reshape(401, nx * ny)
        static_data['velocity_y_reshaped'] = static_data['velocity_y'].reshape(401, nx * ny)
        static_data['vorticity_reshaped'] = static_data['vorticity'].reshape(401, nx * ny)

        # 处理静态力系数数据
        static_data['lift_coeff_sampled'] = np.array(sample_every_ten(static_data['lift_coeff'].T))
        static_data['drag_coeff_sampled'] = np.array(sample_every_ten(static_data['drag_coeff'].T))
        static_data['angle_of_attack_sampled'] = np.array(sample_every_ten(static_data['angle_of_attack'].T))
        static_data['angle_rate_sampled'] = np.array(sample_every_ten(static_data['angle_rate'].T))

        # 加载动态数据
        dynamic_filename = os.path.join(DIR, f'airfoilDNS_a{base_angle}f{freq_str}.h5')

        # 首先加载力系数
        with h5py.File(dynamic_filename, 'r') as data_file:
            dynamic_coeffs = {
                'lift_coeff': np.array(sample_every_ten(data_file['/Cl'][()])),
                'drag_coeff': np.array(sample_every_ten(data_file['/Cd'][()])),
                'angle_of_attack': np.array(sample_every_ten(data_file['/alpha'][()])),
                'angle_rate': np.array(sample_every_ten(data_file['/alphadot'][()]))
            }

        # 加载动态流场数据
        nt = 1001  # 时间步数
        with h5py.File(dynamic_filename, 'r') as data_file:
            dynamic_flow = {
                'airfoil_x': data_file['/xa'][()],
                'airfoil_y': data_file['/ya'][()],
                'velocity_x': data_file['/ux']()[:, 75:275, 69:569][:, ::sparse_value, ::sparse_value],
                'velocity_y': data_file['/uy']()[:, 75:275, 69:569][:, ::sparse_value, ::sparse_value],
                'vorticity': data_file['/vort']()[:, 75:275, 69:569][:, ::sparse_value, ::sparse_value]
            }

        # 处理动态流场数据
        u_magnitude_dynamic = np.sqrt(dynamic_flow['velocity_x']**2 + dynamic_flow['velocity_y']**2)
        dynamic_flow['velocity_magnitude'] = u_magnitude_dynamic
        dynamic_flow['velocity_magnitude_reshaped'] = u_magnitude_dynamic.reshape(nt, nx * ny)
        dynamic_flow['velocity_x_reshaped'] = dynamic_flow['velocity_x'].reshape(nt, nx * ny)
        dynamic_flow['velocity_y_reshaped'] = dynamic_flow['velocity_y'].reshape(nt, nx * ny)
        dynamic_flow['vorticity_reshaped'] = dynamic_flow['vorticity'].reshape(nt, nx * ny)

        print(f"翼型流场数据加载成功 - 静态时间步: 401, 动态时间步: {nt}")

        return {
            'static': static_data,
            'dynamic_coeffs': dynamic_coeffs,
            'dynamic_flow': dynamic_flow,
            'config': config,
            'time_steps': {'static': 401, 'dynamic': nt}
        }

    except Exception as e:
        print(f"加载翼型流场数据时出错: {e}")
        return None

def visualize_airfoil_flow(flow_data, data_type='dynamic', field='vorticity', time_index=0):
    """
    可视化翼型流场

    Parameters:
        flow_data (dict): 翼型流场数据
        data_type (str): 数据类型，'static' 或 'dynamic'
        field (str): 场变量，'vorticity', 'velocity_magnitude', 'velocity_x', 'velocity_y'
        time_index (int): 时间步索引
    """
    print(f"正在可视化翼型{data_type}流场 - {field}场，时间步{time_index}")

    try:
        config = flow_data['config']
        x_sparse = config['grid']['x_sparse']
        y_sparse = config['grid']['y_sparse']

        if data_type == 'static':
            data_source = flow_data['static']
        else:
            data_source = flow_data['dynamic_flow']

        # 选择场变量
        if field == 'vorticity':
            field_data = data_source['vorticity'][time_index]
            field_name = '涡量'
            cmap_choice = cmapw
        elif field == 'velocity_magnitude':
            field_data = data_source['velocity_magnitude'][time_index]
            field_name = '速度幅值'
            cmap_choice = cmapv
        elif field == 'velocity_x':
            field_data = data_source['velocity_x'][time_index]
            field_name = 'X方向速度'
            cmap_choice = cmapv
        elif field == 'velocity_y':
            field_data = data_source['velocity_y'][time_index]
            field_name = 'Y方向速度'
            cmap_choice = cmapv
        else:
            field_data = data_source['vorticity'][time_index]
            field_name = '涡量'
            cmap_choice = cmapw

        # 创建可视化
        plt.figure(figsize=(16, 10), dpi=300)
        contour_plot = plt.contourf(x_sparse, y_sparse, field_data,
                                   levels=20, cmap=cmap_choice, extend='both')
        plt.colorbar(contour_plot, label=field_name)
        plt.xlabel('X坐标')
        plt.ylabel('Y坐标')
        plt.title(f'翼型{data_type}流场{field_name} - 时间步{time_index}')
        plt.axis('equal')
        plt.show()

    except Exception as e:
        print(f"可视化翼型流场时出错: {e}")

# =============================================================================
# 数据导入模块 - 粒子数据和PIV数据
# =============================================================================

def load_particle_data(data_folder=r"D:\数据集\粒子数据\数据", sparse_factor=4, max_files=None):
    """
    加载粒子数据

    Parameters:
        data_folder (str): 粒子数据文件夹路径
        sparse_factor (int): 稀疏化因子，默认为4
        max_files (int): 最大加载文件数，None表示加载所有文件

    Returns:
        dict: 包含粒子数据的字典
    """
    print("正在加载粒子数据...")

    try:
        # 获取所有.dat文件并排序
        files = [f for f in os.listdir(data_folder) if f.endswith('.dat')]
        files.sort(key=lambda x: int(x.split('_')[0]))

        if max_files is not None:
            files = files[:max_files]
            print(f"限制加载文件数量为: {max_files}")

        data_dict = {}
        print(f"开始加载 {len(files)} 个粒子数据文件...")

        # 逐文件读取数据
        for filename in tqdm(files, desc="加载粒子数据文件"):
            file_path = os.path.join(data_folder, filename)

            with open(file_path, 'r') as file:
                time_steps = []
                u, v, w = [], [], []
                x, y = [], []
                pressure, vorticity = [], []

                for line in file:
                    line = line.strip()
                    if line.startswith("Zone"):
                        # 提取时间信息
                        time_step = float(line.split('t="stp:')[1].split('"')[0].strip())
                        time_steps.append(time_step)
                    elif line and not line.startswith(("TITLE", "Variables", "Zone")):
                        # 提取变量数据
                        values = list(map(float, line.split()))
                        if len(values) >= 8:  # 确保有足够的列
                            x.append(values[0])
                            y.append(values[1])
                            u.append(values[2])
                            v.append(values[3])
                            w.append(values[4])
                            pressure.append(values[6])
                            vorticity.append(values[7])

            # 将数据重塑并稀疏化
            if len(u) > 0:
                data_dict[filename] = {
                    'time_steps': np.array(time_steps, dtype=np.float32),
                    'u': np.array(u, dtype=np.float32).reshape(399, 249)[::sparse_factor, ::sparse_factor],
                    'v': np.array(v, dtype=np.float32).reshape(399, 249)[::sparse_factor, ::sparse_factor],
                    'w': np.array(w, dtype=np.float32).reshape(399, 249)[::sparse_factor, ::sparse_factor],
                    'pressure': np.array(pressure, dtype=np.float32).reshape(399, 249)[::sparse_factor, ::sparse_factor],
                    'vorticity': np.array(vorticity, dtype=np.float32).reshape(399, 249)[::sparse_factor, ::sparse_factor]
                }

        # 整合所有文件的数据
        if data_dict:
            data_arrays = {}
            for key in ['u', 'v', 'w', 'pressure', 'vorticity']:
                data_arrays[key] = np.concatenate([data_dict[filename][key] for filename in data_dict])

            all_time_steps = np.concatenate([data_dict[filename]['time_steps'] for filename in data_dict])

            # 计算速度幅值和重塑数据
            uu = data_arrays['u'].reshape(len(all_time_steps), -1)
            vv = data_arrays['v'].reshape(len(all_time_steps), -1)
            ww = data_arrays['w'].reshape(len(all_time_steps), -1)
            velocity_magnitude = (uu**2 + vv**2 + ww**2)**0.5

            vorticity_reshaped = data_arrays['vorticity'].reshape(len(all_time_steps), -1)
            pressure_reshaped = data_arrays['pressure'].reshape(len(all_time_steps), -1)

            # 组合数据
            particle_data = {
                'velocity_components': {
                    'u': uu,
                    'v': vv,
                    'w': ww
                },
                'velocity_magnitude': velocity_magnitude,
                'vorticity': vorticity_reshaped,
                'pressure': pressure_reshaped,
                'time_steps': all_time_steps,
                'combined_features': np.concatenate([velocity_magnitude, vorticity_reshaped, pressure_reshaped], axis=1),
                'config': {
                    'sparse_factor': sparse_factor,
                    'n_files_loaded': len(data_dict),
                    'total_time_steps': len(all_time_steps),
                    'data_folder': data_folder
                }
            }

            print(f"粒子数据加载成功 - 时间步数: {len(all_time_steps)}")
            print(f"数据形状: {particle_data['combined_features'].shape}")

            return particle_data
        else:
            print("没有成功加载任何粒子数据文件")
            return None

    except Exception as e:
        print(f"加载粒子数据时出错: {e}")
        return None

def load_cylinder_piv_data(folder_path='D:/基准数据/MODULO-master/Matlab_Exercises/Exercise_5/Data',
                          sampling_step=100):
    """
    加载圆柱绕流PIV数据

    瞬态条件下流过直径 d = 5 mm、长度 L = 20cm 的圆柱体的流动
    71 × 30 点的网格，空间分辨率约为 Δx = 0.85mm

    Parameters:
        folder_path (str): PIV数据文件夹路径
        sampling_step (int): 采样步长，默认每100个文件采样一次

    Returns:
        dict: 包含PIV数据的字典
    """
    print("正在加载圆柱绕流PIV数据...")

    try:
        # 数据参数
        n_t = 13200  # 总步数
        Fs = 3000    # 采样频率3kHz
        dt = 1 / Fs  # 时间步长
        t = np.arange(0, n_t) * dt  # 时间轴

        # 读取网格文件
        mesh_file = os.path.join(folder_path, 'MESH.dat')
        nxny = np.genfromtxt(mesh_file)  # 导入数据
        nxny = nxny[1:, :]  # 仅取数值数据部分

        # 提取空间坐标
        cy_x = nxny[:, 0]
        cy_y = nxny[:, 1]

        print(f"网格信息: {len(cy_x)} 个空间点")
        print(f"采样参数: 频率={Fs}Hz, 总时间步={n_t}, 采样步长={sampling_step}")

        # 读取流场快照（采样）
        cy_data = []
        sampled_indices = []

        for i in tqdm(range(0, n_t, sampling_step), desc="加载PIV数据文件"):
            filename = f'Res{i:05d}.dat'
            file_path = os.path.join(folder_path, filename)
            if os.path.isfile(file_path):
                data = np.genfromtxt(file_path)
                if len(data.shape) > 1 and data.shape[0] > 1:
                    cy_data.append(data)
                    sampled_indices.append(i)

        if not cy_data:
            print("没有找到有效的PIV数据文件")
            return None

        cy_data = np.array(cy_data)

        # 提取速度分量（跳过第一行标题）
        cy_us = (cy_data[:, 1:, 0]).T  # u速度分量
        cy_vs = (cy_data[:, 1:, 1]).T  # v速度分量

        # 计算不同时间段的平均值
        n_samples = len(sampled_indices)
        third = n_samples // 3

        if n_samples >= 3:
            Amode1_mean = np.mean(cy_us[:, :third], axis=1)
            Amode2_mean = np.mean(cy_us[:, third:2*third], axis=1)
            Amode3_mean = np.mean(cy_us[:, 2*third:], axis=1)
        else:
            Amode1_mean = np.mean(cy_us, axis=1)
            Amode2_mean = Amode1_mean
            Amode3_mean = Amode1_mean

        # 计算速度幅值
        velocity_magnitude = np.sqrt(cy_us**2 + cy_vs**2)

        piv_data = {
            'coordinates': {
                'x': cy_x,
                'y': cy_y
            },
            'velocity': {
                'u': cy_us,
                'v': cy_vs,
                'magnitude': velocity_magnitude
            },
            'time_info': {
                'sampling_frequency': Fs,
                'time_step': dt,
                'sampled_indices': sampled_indices,
                'sampled_times': np.array(sampled_indices) * dt
            },
            'statistics': {
                'mode1_mean': Amode1_mean,
                'mode2_mean': Amode2_mean,
                'mode3_mean': Amode3_mean
            },
            'config': {
                'folder_path': folder_path,
                'sampling_step': sampling_step,
                'total_samples': n_samples,
                'grid_size': len(cy_x)
            }
        }

        print(f"PIV数据加载成功 - 采样点数: {n_samples}, 空间点数: {len(cy_x)}")

        return piv_data

    except Exception as e:
        print(f"加载PIV数据时出错: {e}")
        return None

def visualize_piv_data(piv_data, field='magnitude', time_index=0):
    """
    可视化PIV数据

    Parameters:
        piv_data (dict): PIV数据字典
        field (str): 要可视化的场，'u', 'v', 'magnitude'
        time_index (int): 时间索引
    """
    print(f"正在可视化PIV数据 - {field}场，时间索引{time_index}")

    try:
        x = piv_data['coordinates']['x']
        y = piv_data['coordinates']['y']

        if field == 'u':
            data = piv_data['velocity']['u'][:, time_index]
            field_name = 'U速度分量'
        elif field == 'v':
            data = piv_data['velocity']['v'][:, time_index]
            field_name = 'V速度分量'
        elif field == 'magnitude':
            data = piv_data['velocity']['magnitude'][:, time_index]
            field_name = '速度幅值'
        else:
            data = piv_data['velocity']['magnitude'][:, time_index]
            field_name = '速度幅值'

        plt.figure(figsize=(12, 8), dpi=300)
        scatter = plt.scatter(x, y, c=data, cmap='viridis', s=20)
        plt.colorbar(scatter, label=field_name)
        plt.xlabel('X坐标 (mm)')
        plt.ylabel('Y坐标 (mm)')
        plt.title(f'圆柱绕流PIV数据 - {field_name}')
        plt.axis('equal')
        plt.show()

    except Exception as e:
        print(f"可视化PIV数据时出错: {e}")

# =============================================================================
# 降维方法模块
# =============================================================================

def apply_pca_analysis(data_matrix, n_components=10, visualize=True):
    """
    应用PCA降维分析

    Parameters:
        data_matrix (np.ndarray): 输入数据矩阵，形状为 (时间步, 空间点)
        n_components (int): 主成分数量，默认10
        visualize (bool): 是否可视化结果，默认True

    Returns:
        dict: PCA分析结果
    """
    print(f"正在进行PCA分析 - 主成分数量: {n_components}")

    try:
        from sklearn.preprocessing import StandardScaler

        # 数据标准化
        scaler = StandardScaler()
        data_scaled = scaler.fit_transform(data_matrix)

        # 应用PCA
        pca = PCA(n_components=n_components)
        data_reduced = pca.fit_transform(data_scaled)
        data_reconstructed = pca.inverse_transform(data_reduced)
        data_reconstructed = scaler.inverse_transform(data_reconstructed)

        # 计算重构误差
        reconstruction_error = np.linalg.norm(data_matrix - data_reconstructed) / np.linalg.norm(data_matrix)

        # 计算方差解释比例
        explained_variance_ratio = pca.explained_variance_ratio_
        cumulative_variance = np.cumsum(explained_variance_ratio)

        print(f"PCA分析完成:")
        print(f"- 累积方差解释比例: {cumulative_variance[-1]:.4f}")
        print(f"- 重构误差: {reconstruction_error:.6f}")

        # 可视化结果
        if visualize:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # 方差解释比例
            axes[0, 0].bar(range(1, len(explained_variance_ratio) + 1), explained_variance_ratio)
            axes[0, 0].set_title('各主成分方差解释比例')
            axes[0, 0].set_xlabel('主成分')
            axes[0, 0].set_ylabel('方差解释比例')

            # 累积方差解释比例
            axes[0, 1].plot(range(1, len(cumulative_variance) + 1), cumulative_variance, 'bo-')
            axes[0, 1].axhline(y=0.95, color='r', linestyle='--', label='95%')
            axes[0, 1].set_title('累积方差解释比例')
            axes[0, 1].set_xlabel('主成分数量')
            axes[0, 1].set_ylabel('累积方差解释比例')
            axes[0, 1].legend()

            # 前两个主成分
            axes[1, 0].scatter(data_reduced[:, 0], data_reduced[:, 1],
                              c=range(len(data_reduced)), cmap='viridis', alpha=0.7)
            axes[1, 0].set_title('前两个主成分')
            axes[1, 0].set_xlabel('PC1')
            axes[1, 0].set_ylabel('PC2')

            # 重构对比
            sample_idx = 0
            axes[1, 1].plot(data_matrix[sample_idx, :100], label='原始数据', alpha=0.7)
            axes[1, 1].plot(data_reconstructed[sample_idx, :100], label='重构数据', alpha=0.7)
            axes[1, 1].set_title(f'重构对比 (前100个点)')
            axes[1, 1].set_xlabel('空间点')
            axes[1, 1].set_ylabel('数值')
            axes[1, 1].legend()

            plt.tight_layout()
            plt.show()

        return {
            'method': 'PCA',
            'model': pca,
            'scaler': scaler,
            'data_reduced': data_reduced,
            'data_reconstructed': data_reconstructed,
            'explained_variance_ratio': explained_variance_ratio,
            'cumulative_variance': cumulative_variance,
            'reconstruction_error': reconstruction_error,
            'n_components': n_components
        }

    except Exception as e:
        print(f"PCA分析失败: {e}")
        return None

def apply_ica_analysis(data_matrix, n_components=10, visualize=True):
    """
    应用ICA独立成分分析

    Parameters:
        data_matrix (np.ndarray): 输入数据矩阵
        n_components (int): 独立成分数量，默认10
        visualize (bool): 是否可视化结果，默认True

    Returns:
        dict: ICA分析结果
    """
    print(f"正在进行ICA分析 - 独立成分数量: {n_components}")

    try:
        from sklearn.preprocessing import StandardScaler

        # 数据标准化
        scaler = StandardScaler()
        data_scaled = scaler.fit_transform(data_matrix)

        # 应用ICA
        ica = FastICA(n_components=n_components, random_state=42, max_iter=1000)
        data_reduced = ica.fit_transform(data_scaled)
        data_reconstructed = ica.inverse_transform(data_reduced)
        data_reconstructed = scaler.inverse_transform(data_reconstructed)

        # 计算重构误差
        reconstruction_error = np.linalg.norm(data_matrix - data_reconstructed) / np.linalg.norm(data_matrix)

        print(f"ICA分析完成:")
        print(f"- 重构误差: {reconstruction_error:.6f}")

        # 可视化结果
        if visualize:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # 独立成分时间序列
            for i in range(min(3, n_components)):
                axes[0, 0].plot(data_reduced[:, i], label=f'IC{i+1}', alpha=0.7)
            axes[0, 0].set_title('独立成分时间序列')
            axes[0, 0].set_xlabel('时间步')
            axes[0, 0].set_ylabel('幅值')
            axes[0, 0].legend()

            # 混合矩阵可视化
            im = axes[0, 1].imshow(ica.mixing_[:10, :], cmap='RdBu_r', aspect='auto')
            axes[0, 1].set_title('混合矩阵 (前10行)')
            axes[0, 1].set_xlabel('独立成分')
            axes[0, 1].set_ylabel('观测变量')
            plt.colorbar(im, ax=axes[0, 1])

            # 前两个独立成分
            axes[1, 0].scatter(data_reduced[:, 0], data_reduced[:, 1],
                              c=range(len(data_reduced)), cmap='viridis', alpha=0.7)
            axes[1, 0].set_title('前两个独立成分')
            axes[1, 0].set_xlabel('IC1')
            axes[1, 0].set_ylabel('IC2')

            # 重构对比
            sample_idx = 0
            axes[1, 1].plot(data_matrix[sample_idx, :100], label='原始数据', alpha=0.7)
            axes[1, 1].plot(data_reconstructed[sample_idx, :100], label='重构数据', alpha=0.7)
            axes[1, 1].set_title(f'重构对比 (前100个点)')
            axes[1, 1].set_xlabel('空间点')
            axes[1, 1].set_ylabel('数值')
            axes[1, 1].legend()

            plt.tight_layout()
            plt.show()

        return {
            'method': 'ICA',
            'model': ica,
            'scaler': scaler,
            'data_reduced': data_reduced,
            'data_reconstructed': data_reconstructed,
            'mixing_matrix': ica.mixing_,
            'reconstruction_error': reconstruction_error,
            'n_components': n_components
        }

    except Exception as e:
        print(f"ICA分析失败: {e}")
        return None

def compare_dimensionality_reduction_methods(data_matrix, n_components=10):
    """
    比较多种降维方法

    Parameters:
        data_matrix (np.ndarray): 输入数据矩阵
        n_components (int): 降维后的维数，默认10

    Returns:
        dict: 各种方法的比较结果
    """
    print(f"正在比较多种降维方法 - 降维维数: {n_components}")

    methods_results = {}

    # PCA分析
    print("\n1. PCA分析...")
    pca_result = apply_pca_analysis(data_matrix, n_components, visualize=False)
    if pca_result:
        methods_results['PCA'] = pca_result

    # ICA分析
    print("\n2. ICA分析...")
    ica_result = apply_ica_analysis(data_matrix, n_components, visualize=False)
    if ica_result:
        methods_results['ICA'] = ica_result

    # 创建比较图表
    if len(methods_results) > 0:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 重构误差比较
        methods = list(methods_results.keys())
        errors = [methods_results[method]['reconstruction_error'] for method in methods]

        axes[0, 0].bar(methods, errors)
        axes[0, 0].set_title('重构误差比较')
        axes[0, 0].set_ylabel('相对重构误差')

        # 降维结果散点图比较
        colors = ['blue', 'red', 'green', 'orange']
        for i, method in enumerate(methods):
            data_reduced = methods_results[method]['data_reduced']
            axes[0, 1].scatter(data_reduced[:, 0], data_reduced[:, 1],
                              alpha=0.6, label=method, color=colors[i % len(colors)])
        axes[0, 1].set_title('降维结果比较 (前两个成分)')
        axes[0, 1].set_xlabel('成分1')
        axes[0, 1].set_ylabel('成分2')
        axes[0, 1].legend()

        # 重构对比
        sample_idx = 0
        axes[1, 0].plot(data_matrix[sample_idx, :100], label='原始数据',
                       linewidth=2, color='black')
        for i, method in enumerate(methods):
            data_reconstructed = methods_results[method]['data_reconstructed']
            axes[1, 0].plot(data_reconstructed[sample_idx, :100],
                           label=f'{method}重构', alpha=0.7, color=colors[i % len(colors)])
        axes[1, 0].set_title('重构对比 (前100个点)')
        axes[1, 0].set_xlabel('空间点')
        axes[1, 0].set_ylabel('数值')
        axes[1, 0].legend()

        # 方差解释比例（仅PCA）
        if 'PCA' in methods_results:
            cumulative_variance = methods_results['PCA']['cumulative_variance']
            axes[1, 1].plot(range(1, len(cumulative_variance) + 1),
                           cumulative_variance, 'bo-')
            axes[1, 1].axhline(y=0.95, color='r', linestyle='--', label='95%')
            axes[1, 1].set_title('PCA累积方差解释比例')
            axes[1, 1].set_xlabel('主成分数量')
            axes[1, 1].set_ylabel('累积方差解释比例')
            axes[1, 1].legend()
        else:
            axes[1, 1].text(0.5, 0.5, '无PCA结果', ha='center', va='center',
                           transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('PCA结果')

        plt.tight_layout()
        plt.show()

        # 打印比较结果
        print("\n" + "=" * 50)
        print("降维方法比较结果")
        print("=" * 50)
        for method, result in methods_results.items():
            print(f"{method}:")
            print(f"  - 重构误差: {result['reconstruction_error']:.6f}")
            if 'cumulative_variance' in result:
                print(f"  - 方差解释比例: {result['cumulative_variance'][-1]:.4f}")
            print()

    return methods_results
# =============================================================================
# 原始代码部分（保留用于参考和临时使用）
# =============================================================================

#=========================================数据集选择和导入============================
"""
论文数据集 - 原始实现代码
注意：以下是原始的数据处理代码，保留所有注释内容
建议使用上面定义的模块化函数进行数据处理
"""
#=========================================Re=100时圆柱绕流A============================
folder_path = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/circular_cylinder_0112'#稳态
folder_path = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/cylinder_data'#非稳态
file_list = [file for file in os.listdir(folder_path) if file.startswith("plt_") and file.endswith(".dat")]
data_cylinder_snapshot_A = []
for file_name in file_list:
    file_path = os.path.join(folder_path, file_name)
    file_data = np.loadtxt(file_path, skiprows=2)
    data_cylinder_snapshot_A.append(file_data)
#第一行为x坐标，第二行为y坐标，第三行为密度，第四行为速度u，第五行为速度v，第六行为压力，第七行为涡量。   
data_cylinder_snapshot_A = np.array(data_cylinder_snapshot_A)

#网格布置
nx=int(((34.98-0.02)/0.02)+2)
ny=int(((29.98-0.02)/0.02)+1)
print(nx*ny)
#稀疏化策略
sparse=4
#数据定义
X_cylinder_A=data_cylinder_snapshot_A[1,:,0].reshape(ny,nx)[549:949,449:1349][::sparse, ::sparse]
Y_cylinder_A=data_cylinder_snapshot_A[1,:,1].reshape(ny,nx)[549:949,449:1349][::sparse, ::sparse]
U_cylinder_A=data_cylinder_snapshot_A[:,:,3].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse, ::sparse]
V_cylinder_A=data_cylinder_snapshot_A[:,:,4].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse, ::sparse]
P_cylinder_A=data_cylinder_snapshot_A[:,:,5].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse, ::sparse]
W_cylinder_A=data_cylinder_snapshot_A[:,:,6].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse, ::sparse]

#流场快照可视化
for i in range (0,1):
    plt.figure(figsize=(40, 16),dpi=300)
    z_cylinder_A = W_cylinder_A[i,:]
    clev = np.arange(-0.4, 0.45, 0.1)
    cf = plt.contourf(X_cylinder_A, Y_cylinder_A,z_cylinder_A,clev,cmap=cmap,extend='both')
    plt.xlim(X_cylinder_A.min(), X_cylinder_A.max())
    plt.ylim(Y_cylinder_A.min(), Y_cylinder_A.max())
    cbar = plt.colorbar(cf)
    cbar.set_label('Values')
    plt.xlabel('X Label')
    plt.ylabel('Y Label')
    plt.show()

#力的提取
sparse=6#6
file_path = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/circular_cylinder_0112/drag.dat'
#第一行为计算步数，换算成时间为“步数*0.02*0.1“，第二行为阻力系数，第三行为升力系数。
data_cylinder_force_A = np.loadtxt(file_path, skiprows=1)
time_cylinder_A=data_cylinder_force_A[5049:,0][::sparse]*0.02*0.1
cl_cylinder_A=data_cylinder_force_A[5049:,1][::sparse]
cd_cylinder_A=data_cylinder_force_A[5049:,2][::sparse]
#力系数可视化
plt.plot(time_cylinder_A,cl_cylinder_A)
plt.plot(time_cylinder_A,cd_cylinder_A)

sparse=50#50
file_path = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/cylinder_data/drag.dat'
#第一行为计算步数，换算成时间为“步数*0.02*0.1“，第二行为阻力系数，第三行为升力系数。
data_cylinder_force_A = np.loadtxt(file_path, skiprows=1)
time_cylinder_A=data_cylinder_force_A[49:,0][::sparse]*0.02*0.1
cl_cylinder_A=data_cylinder_force_A[49:,1][::sparse]
cd_cylinder_A=data_cylinder_force_A[49:,2][::sparse]
#力系数可视化
plt.plot(time_cylinder_A,cl_cylinder_A)
plt.plot(time_cylinder_A,cd_cylinder_A)
# =========================================Re=100时圆柱绕流B===============================
# data_cylinder_snapshot_B = loadmat("D:/基准数据/DATA/DATA/FLUIDS/CYLINDER_ALL.mat")
# #构建网格数据
# def generate_grid(x_range, y_range):
#     grid_points = []
#     for y in y_range:
#         for x in x_range:
#             grid_points.append((x, y))
#     return grid_points
# #指定x和y的范围
# x_range =  np.linspace(-1, 9, 449)# -1到9的范围
# y_range =  np.linspace(-2, 2, 199)# 0到2的范围
# #生成二维网格点的坐标分布
# grid_points = np.array(generate_grid(x_range, y_range))# 位置向量矩阵，r = (xi,yj)
# dt=0.2#时间差

# X_cylinder_B=grid_points[:,0].reshape(199,449)# 位置矩阵x，xi,xj
# Y_cylinder_B=grid_points[:,1].reshape(199,449)# 位置矩阵y，yi,yj

# U_cylinder_B = data_cylinder_snapshot_B["UALL"]  # N x T：速度X
# U_cylinder_B_mean=np.mean(U_cylinder_B, axis=1)

# tstep=10#步骤
# U_cylinder_B_plu =U_cylinder_B[:,tstep]-U_cylinder_B_mean
# U_cylinder_B_plu_mean=np.mean(abs(U_cylinder_B_plu.reshape(449,199).T), axis=0)
# #单点曲线
# U_cylinder_B_snapshot1=U_cylinder_B.reshape(449,199,151)[157,134,:]
# plt.plot(U_cylinder_B_snapshot1)

# V_cylinder_B = data_cylinder_snapshot_B["VALL"]  # N x T: 速度Y
# V_cylinder_B_mean=np.mean(V_cylinder_B, axis=1)
# V_cylinder_B_plu =V_cylinder_B[:,tstep]-V_cylinder_B_mean
# V_cylinder_B_plu_mean=np.mean(abs(V_cylinder_B_plu.reshape(449,199).T), axis=0)

# W_cylinder_B = data_cylinder_snapshot_B["VORTALL"]  #N x T: 涡量

# #可视化
# z=U_cylinder_B[:,tstep].reshape(449,199).T
# z=U_cylinder_B_mean.reshape(449,199).T
# z=U_cylinder_B_plu.reshape(449,199).T
# fig1, ax1 = plt.subplots(figsize = (16, 9))
# circle = plt.Circle((0.09, 0), 0.52, color = 'black')
# clev = np.arange(-0.4, 0.45, 0.1)
# cnt1 = ax1.contourf(X_cylinder_B, Y_cylinder_B, z, clev, cmap = plt.cm.coolwarm, extend='both')
# ax1.add_patch(circle)
# plt.axis('equal')
# fig1.colorbar(cnt1)

# =========================================Re=100时2D圆柱绕流C===============================
data_cylinder_snapshot_C = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D.mat")#10边型的局部观测
#data_cylinder_snapshot_C = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_flower.mat")#10边型的局部观测
#时间差0.08s
sparse=1
C_star = data_cylinder_snapshot_C["C_star"][::sparse,:]  # N x 2 x T：浓度C
U_star = data_cylinder_snapshot_C["U_star"][::sparse,:]  # N x 2 x T：速度X
V_star = data_cylinder_snapshot_C["V_star"][::sparse,:]  # N x 2 x T：速度Y
P_star = data_cylinder_snapshot_C["P_star"][::sparse,:]   # N x T:压力
T_star = data_cylinder_snapshot_C["t_star"][::sparse,:]   # T x 1:时间
X_star = data_cylinder_snapshot_C["x_star"][::sparse,:]  # N x 2：位置：X,Y
Y_star = data_cylinder_snapshot_C["y_star"][::sparse,:]  # N x 2：位置：X,Y

#绘制中跨度切面的快照
plt.figure()
fig1, ax1 = plt.subplots(figsize=(16, 9),dpi=300)
plt.tripcolor(X_star[:,0], Y_star[:,0],U_star[:,100], shading='gouraud', cmap=cmapv)
plt.axis('equal')
plt.colorbar()
plt.show()
# =========================================Re=100时2D圆柱绕流C(时间差影响)===============================
# data_cylinder_snapshot_C1 = loadmat("D:/基准数据/圆柱绕流数据集/HPM-master/Data/cylinder.mat")##时间差0.2s
# data_cylinder_snapshot_C2 = loadmat("D:/基准数据/圆柱绕流数据集/HPM-master/Data/cylinder_fine.mat")#时间差0.02s
# data_cylinder_snapshot_C3 = loadmat("D:/基准数据/圆柱绕流数据集/HPM-master/Data/cylinder_finer.mat")#时间差0.01s
# data_cylinder_snapshot_C4 = loadmat("D:/基准数据/圆柱绕流数据集/HPM-master/Data/cylinder_finest.mat")#时间差0.005s

# U_star = data_cylinder_snapshot_C1["U_star"][:,0,:]  # N x 2 x T：速度X
# V_star = data_cylinder_snapshot_C1["U_star"][:,1,:]  # N x 2 x T：速度Y
# T_star = data_cylinder_snapshot_C1["t_star"]  # T x 1:时间
# X_star = data_cylinder_snapshot_C1["X_star"][:,0] # N x 2：位置：X,Y
# Y_star = data_cylinder_snapshot_C1["X_star"][:,1] # N x 2：位置：X,Y

# #绘制中跨度切面的快照
# plt.figure()
# fig1, ax1 = plt.subplots(figsize=(16, 9),dpi=300)
# plt.tripcolor(X_star[:,], Y_star[:,],U_star[:,100], shading='gouraud', cmap=cmapv)
# plt.axis('equal')
# plt.colorbar()
# plt.show()

# =========================================Re=100时圆柱绕流D(边界影响)===============================
# data_cylinder_snapshot_D = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_Re200Pec2000_Dirichlet_Streaks.mat")
# data_cylinder_snapshot_D = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_Re200Pec2000_Neumann_Streaks.mat")
# data_cylinder_snapshot_D = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_Re200Pec200_Dirichlet_Streaks_Forces.mat")
# data_cylinder_snapshot_D = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_Re200Pec200_Neumann_Streaks_Forces.mat")
# C_data  = data_cylinder_snapshot_D["C_data"]  # N x 2 x T：浓度C
# U_data  = data_cylinder_snapshot_D["U_data"]  # N x 2 x T：速度X
# V_data  = data_cylinder_snapshot_D["V_data"]  # N x 2 x T：速度Y
# P_data  = data_cylinder_snapshot_D["P_data"]  # N x T:压力
# T_data  = data_cylinder_snapshot_D["t_data"]  # T x 1:时间
# X_data  = data_cylinder_snapshot_D["x_data"]  # N x 2：位置：X,Y
# Y_data  = data_cylinder_snapshot_D["y_data"] # N x 2：位置：X,Y
# #绘制中跨度切面的快照
# plt.figure()
# fig1, ax1 = plt.subplots(figsize=(16, 9),dpi=300)
# plt.tripcolor(X_data[:,0], Y_data[:,0],U_data[:,100], shading='gouraud', cmap=cmapv)
# plt.axis('equal')
# plt.colorbar()
# plt.show()

# =========================================Re=100时圆柱绕流E(3D)===============================
data_cylinder_snapshot_E = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder3D.mat")#3D

C_star = data_cylinder_snapshot_E["C_star"]  # N x 2 x T：浓度C
U_star = data_cylinder_snapshot_E["U_star"]  # N x 2 x T：速度X
V_star = data_cylinder_snapshot_E["V_star"]  # N x 2 x T：速度Y
W_star = data_cylinder_snapshot_E["W_star"] # N x 2 x T：速度Z
P_star = data_cylinder_snapshot_E["P_star"]  # N x T:压力
T_star = data_cylinder_snapshot_E["t_star"]  # T x 1:时间
X_star = data_cylinder_snapshot_E["x_star"]  # N x 2：位置：X,Y
Y_star = data_cylinder_snapshot_E["y_star"] # N x 2：位置：X,Y
Z_star = data_cylinder_snapshot_E["z_star"] # N x 2：位置：X,Y
# =========================================其他绕流===============================
#data_cylinder_snapshot_F = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Davinci.mat")#10边型的局部观测
#data_cylinder_snapshot_F = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/real_aneurysm.mat")#10边型的局部观测
#data_cylinder_snapshot_F = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/real_aneurysm_shear.mat")#10边型的局部观测
#data_cylinder_snapshot_F = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Stenosis2D.mat")#10边型的局部观测
# C_star = data_cylinder_snapshot_F["C_star"]  # N x 2 x T：浓度C
# U_star = data_cylinder_snapshot_F["U_star"]  # N x 2 x T：速度X
# V_star = data_cylinder_snapshot_F["V_star"]  # N x 2 x T：速度Y
# P_star = data_cylinder_snapshot_F["P_star"]  # N x T:压力
# T_star = data_cylinder_snapshot_F["t_star"]  # T x 1:时间
# X_star = data_cylinder_snapshot_F["x_star"]  # N x 2：位置：X,Y
# Y_star = data_cylinder_snapshot_F["y_star"] # N x 2：位置：X,Y

# #绘制中跨度切面的快照
# plt.figure()
# fig1, ax1 = plt.subplots(figsize=(16, 9),dpi=300)
# plt.tripcolor(X_star[:,0], Y_star[:,0],C_star[:,0], shading='gouraud', cmap=cmapv)
# plt.axis('equal')
# plt.colorbar()
# plt.show()

# =========================================低雷诺数的俯仰翼型===============================
plt.close('all')
DIR = 'D:\基准数据\低雷诺数的俯仰翼'
# 相关参数
paramsFile = h5py.File(os.path.join(DIR, 'airfoilDNS_parameters.h5'), 'r')
dt_field = paramsFile['/dt_field'][()]
dt_force = paramsFile['/dt_force'][()] 
Re = paramsFile['/Re'][()]
FreqsAll = paramsFile['/frequencies'][()]
alpha_p = paramsFile['/alpha_p'][()] 
alpha_0s = paramsFile['/alpha_0s'][()]
pitch_axis = paramsFile['/pitch_axis'][()]
paramsFile.close()

BaseAngle = 30 
tstep = 5

filenameGrid = os.path.join(DIR, 'airfoilDNS_grid.h5')
with h5py.File(filenameGrid, 'r') as gridFile:
    x = gridFile['/x'][()]
    y = gridFile['/y'][()]
    nx = len(x)
    ny = len(y)
    
# 数重采样
def sample_every_ten(numbers):
    new_sequence = numbers[::10]
    return new_sequence

# 数据稀疏化
sparse_value=2

#nx = int(len(x[69:569])/sparse_value)
nx = int(len(x[69:569])/sparse_value)
ny = int(len(y[75:275])/sparse_value)

#plt_x=x[69:569][::sparse_value]
plt_x=x[69:569][::sparse_value]
plt_y=y[75:275][::sparse_value]
    
#数据选择
#FreqStr = '0p0'   # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']#非周期
#FreqStr = '0p05'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']#非周期
#FreqStr = '0p1'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']准周期
#FreqStr = '0p2'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期
#FreqStr = '0p25'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期
#FreqStr = '0p3'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期
#FreqStr = '0p35'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期
#FreqStr = '0p4'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']#准周期
#FreqStr = '0p5'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期

FreqStrs=['0p0','0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']
FreqStr = '0p2'# ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']
Freq = 0# [0.05,0.1,0.2, 0.25,0.3,0.35,0.4,0.5]

filename = os.path.join(DIR, f'airfoilDNS_a30static.h5')
#filename = os.path.join(DIR, f'airfoilDNS_a25static.h5')
with h5py.File(filename, 'r') as dataFile:
    Cl = dataFile['/Cl'][()]
    Cd = dataFile['/Cd'][()]
    alpha = dataFile['/alpha'][()]
    alphadot = dataFile['/alphadot'][()]
    xa = dataFile['/xa'][()]
    ya = dataFile['/ya'][()]
    ux = dataFile['/ux'][()]
    uy = dataFile['/uy'][()]
    vort=dataFile['/vort'][()]
    ux = ux[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    uy = uy[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    vort = vort[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    u = np.sqrt(ux * ux + uy * uy)
    ureshape = u.reshape(401, nx * ny)
    uxreshape = (ux).reshape(401, nx * ny)
    uyreshape = (uy).reshape(401, nx * ny)
    vortreshape = (vort).reshape(401, nx * ny)
    cl= np.array(sample_every_ten(Cl.T))
    cd = np.array(sample_every_ten(Cd.T))
    alpha = np.array(sample_every_ten(alpha.T))
    alphadot = np.array(sample_every_ten(alphadot.T))   

filename = os.path.join(DIR, f'airfoilDNS_a{BaseAngle}f{FreqStr}.h5')
with h5py.File(filename, 'r') as dataFile:
    Cl = dataFile['/Cl'][()]
    Cd = dataFile['/Cd'][()]
    alpha = dataFile['/alpha'][()]
    alphadot = dataFile['/alphadot'][()]
    cl= np.array(sample_every_ten(Cl))
    cd = np.array(sample_every_ten(Cd))
    alpha = np.array(sample_every_ten(alpha))
    alphadot = np.array(sample_every_ten(alphadot))   
#nt = 401   
nt = 1001
filename = os.path.join(DIR, f'airfoilDNS_a{BaseAngle}f{FreqStr}.h5')
with h5py.File(filename, 'r') as dataFile:
    xa = dataFile['/xa'][()]
    ya = dataFile['/ya'][()]
    ux = dataFile['/ux'][()]
    uy = dataFile['/uy'][()]
    vort = dataFile['/vort'][()]
    ux = ux[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    uy = uy[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    vort = vort[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    u = np.sqrt(ux * ux + uy * uy)
    ureshape = u.reshape(nt, nx * ny)
    uxreshape = (ux).reshape(nt, nx * ny)
    uyreshape = (uy).reshape(nt, nx * ny)
    vortreshape = (vort).reshape(nt, nx * ny)
    
#=============================================================================
i=1
#可视化
plt.figure(figsize=(20, 6),dpi=200) 
z = ux[36, :, :]
z = uy[36, :, :]
z = vort[36, :, :]
cf = plt.contourf(plt_x, plt_y, z, 
                  levels=np.linspace(-3, 3, 32), vmin=-3, vmax=3,
                  cmap='RdBu_r',extend='both')
cf.set_clim(-3, 3)
cbar = plt.colorbar(cf)
cbar.set_label('Values')
plt.xlabel('X Label')
plt.ylabel('Y Label')
plt.show()
# #=============================================================================
# import imageio
# images = []
# for tstep in range(0, 1, 5):
#     plt.figure(figsize=(10, 4))  # 调整图像尺寸
#     z = vort[tstep, :]
#     cf = plt.contourf(x[:], y[74:276], z,
#                       levels=np.linspace(-3, 3, 18), vmin=-3, vmax=3,
#                       cmap='RdBu_r', extend='both')
#     cf.set_clim(-3, 3)
#     #plt.plot(xa[:, tstep], ya[:, tstep], 'k-')
#     plt.axis('off')  # 关闭坐标轴显示
#     # plt.xlim(x.min(), x.max())
#     # plt.ylim(y[74], y[276])
#     # cbar = plt.colorbar(cf)
#     # cbar.set_label('Values')
#     # plt.xlabel('X', fontsize=15)  # 调整轴标签字体大小
#     # plt.ylabel('Y', fontsize=15)  # 调整轴标签字体大小
#     # plt.title('Title', fontsize=20)  # 添加图像标题，调整字体大小
#     plt.tight_layout()  # 调整布局，防止标签被切断
#     # 保存每一帧图像
#     filename = f'frame_{tstep:03d}.png'
#     plt.savefig(filename, dpi=800)  # 调整保存图像的分辨率
#     # 将当前图像添加到图像列表中
#     images.append(imageio.imread(filename))
#     # 关闭当前图像，准备绘制下一帧
#     plt.close()
# # 使用imageio库将图像列表保存为GIF
# imageio.mimsave('animation.gif', images, duration=0.1)
# # 删除临时生成的单独图像文件
# for filename in set(f'frame_{tstep:03d}.png' for tstep in range(0, 1000, 5)):
#     os.remove(filename)

# #获取监测点数据
# ux1=ux[:,75,174]
# ux2=ux[:,100,174]
# ux3=ux[:,125,174]
# time=np.arange(50, 150.1, 0.1)#预测时间
# #可视化
# fig, (ax1, ax2, ax3) = plt.subplots(3, sharex=True,dpi=1200)
# ax1.plot(time, ux1, color='black',label=r"$A$")
# ax2.plot(time, ux2, color='black',label=r"$B$")
# ax3.plot(time, ux3, color='black',label=r"$C$")
# ax3.set_xlabel(r"$t$")
# plt.show()
# =========================================二、数据集的导入===============================
#圆柱绕流PIV
#瞬态条件下流过直径 d = 5 mm、长度 L = 20cm 的圆柱体的流动
# 1. 数据准备
#FOLDER = 'C:/Users/<USER>/Desktop/降维算法的评价/Ex_5_TR_PIV_Cylinder'
FOLDER =  'D:/基准数据/MODULO-master/Matlab_Exercises/Exercise_5/Data'
n_t = 13200  # 步数
Fs = 3000
dt = 1 / Fs  # 数据采样频率为3kHz
t = np.arange(0, n_t) * dt  # 准备时间轴
#71 × 30 点的网格,空间分辨率约为 Δx = 0.85mm

# 读取一个网格文件
file = os.path.join(FOLDER, 'MESH.dat')
nxny = np.genfromtxt(file)  # 导入数据
nxny = nxny[1:, :]  # 仅取数值数据部分

# 这是一个矢量数据的示例，因此空间点的数量应该加倍
cy_x = nxny[:, 0]
cy_y = nxny[:, 1]

#0-4000
#4000-7000
#7000-
# 读取流场快照
cy_data=[]
for i in range(0, 13200):
    filename = f'Res{i:05d}.dat'
    file_path = os.path.join(FOLDER, filename)
    if os.path.isfile(file_path):
        cy_data.append(np.genfromtxt(file_path))
    
cy_data=np.array(cy_data)    
cy_us=(cy_data[:,1:,0]).T
cy_vs=(cy_data[:,1:,1]).T

Amode1_mean=np.mean(cy_us[:, :4000], axis=1)
Amode2_mean=np.mean(cy_us[:, 4000:7000], axis=1)
Amode3_mean=np.mean(cy_us[:, 7000:], axis=1)

#1236,观察速度变化：x,y=30,2
#2500
#5000
#10000
# Au=cy_us[1236, ]
# Av=cy_vs[1236, ]
# snapshot_u=(cy_us[:, 10000]).reshape(30,71)
# snapshot_v=(cy_vs[:, 10000]).reshape(30,71)
# A1=snapshot_u-z1
# A2=snapshot_v-z2
# A1_mean=np.mean(A1, axis=0)
# A2_mean=np.mean(A2, axis=0)
# plt.tripcolor(A1, shading='gouraud',cmap=cmap)

# 绘制中跨度切面的快照 
fig1, ax1 = plt.subplots(figsize = (16, 9))
circle = plt.Circle((-0.5, 0), 5, color = 'black')
cnt1 = ax1.tripcolor(cy_x, cy_y, cy_us[:, 2500],vmin=-2.5,vmax=15,  cmap = cmap, shading='gouraud', )
ax1.add_patch(circle)
plt.axis('equal')
fig1.colorbar(cnt1)
plt.savefig('high_resolution_plot.png', dpi=300) 

# 绘制中跨度切面的快照
fig1, ax1 = plt.subplots(figsize = (16, 9))
circle = plt.Circle((-0.5, 0), 5, color = 'black')
cnt1 = ax1.tripcolor(cy_x, cy_y, Amode1_mean,vmin=-2.5,vmax=15,  cmap = cmap, shading='gouraud', )
ax1.add_patch(circle)
plt.axis('equal')
fig1.colorbar(cnt1)
plt.savefig('high_resolution_plot.png', dpi=300)
#=========================================NACA0012湍流数据集的可压缩流动===============================
plt.close('all')
DIR = 'D:\基准数据\尾流的大涡模拟'

# 读取参数
filename_parameters = DIR + '/airfoilLES_parameters.h5'
with h5py.File(filename_parameters, 'r') as f:
    Re = f['/Re'][()]  # 雷诺数
    dt = f['/dt'][()]  # 快照之间的时间步长（传导时间单位）

# 读取网格信息
filename_grid = DIR + '/airfoilLES_grid.h5'
with h5py.File(filename_grid, 'r') as f:
    x = f['/x'][()]  # 流场网格的x坐标 
    y = f['/y'][()]  # 流场网格的y坐标
    xa = f['/xa'][()]  # 翼型网格的x坐标
    ya = f['/ya'][()]  # 翼型网格的y坐标
    w = f['/w'][()]  # 流场网格的单元体积

# 读取时间平均的中跨度流场信息
filename_mean_midspan = DIR + '/airfoilLES_mean_midspan.h5'
with h5py.File(filename_mean_midspan, 'r') as f:
    ux_mean = f['/ux_mean'][()]  # x方向速度的平均值
    uy_mean = f['/uy_mean'][()]  # y方向速度的平均值
    uz_mean = f['/uz_mean'][()]  # z方向速度的平均值

# 绘制中跨度流场的平均流动
# dx=x[1]-x[2]
# nx=(max(x)-min(x))
# print(nx)
# dy=y[1]-y[2]
# ny=(max(y)-min(y))/dy
# print(ny)

plt.figure()
fig1, ax1 = plt.subplots(figsize=(16, 9))
plt.tripcolor(x, y, ux_mean, shading='gouraud', cmap=cmapv)
plt.fill(xa, ya, color="white")
plt.plot(xa, ya, color='black', linewidth=2.0)
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])
#plt.clim([-0.2, 1.0])
plt.colorbar()
plt.show()

# 读取时间和z方向平均的流场信息
# filename_mean_zavg = DIR + '/airfoilLES_mean_zavg.h5'
# with h5py.File(filename_mean_zavg, 'r') as f:
#     ux_zavg_mean = f['/ux_zavg_mean'][()]  # x方向速度的时间和z方向平均值
#     uy_zavg_mean = f['/uy_zavg_mean'][()]  # y方向速度的时间和z方向平均值
#     uz_zavg_mean = f['/uz_zavg_mean'][()]  # z方向速度的时间和z方向平均值

# # 绘制时间和z方向平均的流场
# plt.figure()
# plt.scatter(x, y, 10, ux_zavg_mean,)#数值坐标
# plt.fill(xa, ya, [0.6, 0.6, 0.6],color="white")#翼型坐标
# plt.axis('equal')
# plt.box(True)
# plt.axis([-0.2, 2, -1, 1])
# plt.clim([-0.2, 1.2])
# plt.colorbar()

# 读取流场快照
jt_list = range(5000, 10000)
jj = 0
ux_nacas=[]
uy_nacas=[]
uz_nacas=[]

# 读取中跨度的快照
for jt in jt_list:
    jj += 1
    filename = DIR + '/airfoilLES_midspan/airfoilLES_t{:05d}.h5'.format(jt)
    with h5py.File(filename, 'r') as f:
        ux_naca = f['/ux'][()]  # x方向速度
        ux_nacas.append(ux_naca)
        uy_naca = f['/uy'][()]  # y方向速度
        uy_nacas.append(uy_naca)
        uz_naca = f['/uz'][()]  # z方向速度
        uz_nacas.append(uz_naca)
        
ux_nacas=(np.array(ux_nacas)).T #N*T
uy_nacas=(np.array(uy_nacas)).T #N*T
uz_nacas=(np.array(uz_nacas)).T #N*T
 
tstep=10
# 绘制中跨度切面的快照
plt.figure()
fig1, ax1 = plt.subplots(figsize=(16, 9))
plt.tripcolor(x, y, uz_nacas[:,10], shading='gouraud', cmap=cmapv)
plt.fill(xa, ya, color="white")
plt.plot(xa, ya, color='black', linewidth=2.0)
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])
#plt.clim([-0.2, 1.0])
plt.colorbar()
plt.show()

plt.figure()
fig1, ax1 = plt.subplots(figsize=(16, 9))
plt.tripcolor(x, y, uz_nacas[:,10]-ux_mean, shading='gouraud', cmap=cmapv)
plt.fill(xa, ya, color="white")
plt.plot(xa, ya, color='black', linewidth=2.0)
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])
#plt.clim([-0.2, 1.0])
plt.colorbar()
plt.show()

# =========================================空化数据集的导入==============================
from Extract_Data_RANS2022 import Data_class#原始数据位置已调整#887
from Extract_Data_RANS2023 import Data_class#原始数据位置已调整#1866
from Extract_Data_DES import Data_class#原始数据位置已调整#1093
from Extract_Data_LES import Data_class#原始数据位置已调整#819
time_num  = 887#时间数887，LES819
row_num   = 125#行数
num_ppr   = 500#列数
Reduction = 0#列数减少

#2.数据类型
fieldV = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='速度')
fieldVX = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='X速度')
fieldVY = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Y速度')
fieldVZ = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Z速度')
fieldW = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='涡量')
fieldWX = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='X涡量')
fieldWY = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Y涡量')
fieldWZ = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Z涡量')
fieldP = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='压力')
fieldD = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='密度')
fieldwater = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='水体积分数')
fieldvapor = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='水蒸汽体积分数')

#3.数据定义
#3.1原始数据1：数据平铺*时间
V = fieldV.extract_data()[:,:time_num]
P = fieldP.extract_data()[:,:time_num]
W = fieldW.extract_data()[:,:time_num]
Water = fieldwater.extract_data()[:,:time_num]
#Vapor = fieldvapor.extract_data()[:,:time_num]
#D  = fieldD.extract_data()[:,:time_num]
#VX = fieldVX.extract_data()[:,:time_num]
#VY = fieldVY.extract_data()[:,:time_num]
#VZ = fieldVZ.extract_data()[:,:time_num]
#WX = fieldWX.extract_data()[:,:time_num]
#WY = fieldWY.extract_data()[:,:time_num]
#WZ = fieldWZ.extract_data()[:,:time_num]

#3.2原始数据2：数据：X*Y*时间
#3.3原始数据3：数据：X*Y*时间(数据选择)RANS:0-221,一个周期,121-296 296-475
#52-270 270-431
time_num1=188#105
time_num2=823#431
num=time_num2-time_num1#选取的数据个数

# 数据稀疏化
sparse_value=2
V2=np.array(V).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
P2=np.array(P).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
W2=np.array(W).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
Water2=np.array(Water).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# Vapor2=np.array(Vapor).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# D2  = np.array(D).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# VX2  = np.array(VX).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# VY2  = np.array(VY).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# VZ2  = np.array(VZ).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# WX2  = np.array(WX).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# WY2  = np.array(WY).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# WZ2  = np.array(WZ).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
#sns.heatmap(round(pd.DataFrame(V2[:,:,2]),2),cmap='RdBu_r')

#3.0原始数据0：数据：空间*时间
V1 = V2.reshape(-1,num)
P1 = P2.reshape(-1,num)
W1 = W2.reshape(-1,num)
Water1 = Water2.reshape(-1,num)
# Vapor1 = Vapor2.reshape(-1,num)
# D1 = D2.reshape(-1,num)
# VX1 = VX2.reshape(-1,num)
# VY1 = VY2.reshape(-1,num)
# VZ1 = VZ2.reshape(-1,num)
# WX1 = WX2.reshape(-1,num)
# WY1 = WY2.reshape(-1,num)
# WZ1 = WZ2.reshape(-1,num)

#3.6空间维度和变量维度均缩减平铺：空间变量均降维，变量标准化。
from sklearn.preprocessing import StandardScaler,MinMaxScaler#标准化函数
transfer1=StandardScaler()#标准化函数Z-score标准化方法
transfer2=MinMaxScaler()#标准化函数Z-score标准化方法
data_new=[]
for j in range(num):
    V2=V1[:,j].reshape(-1,1)
    P2=P1[:,j].reshape(-1,1)
    W2=W1[:,j].reshape(-1,1)
    Water2=Water1[:,j].reshape(-1,1)
    #Vapor2=Vapor1[:,j].reshape(-1,1)
    #D2=D1[:,j].reshape(-1,1)
    #VX2=VX1[:,j].reshape(-1,1)
    #VY2=VY1[:,j].reshape(-1,1)
    #VZ2=VZ1[:,j].reshape(-1,1)
    #WX2=WX1[:,j].reshape(-1,1)
    #WY2=WY1[:,j].reshape(-1,1)
    #WZ2=WZ1[:,j].reshape(-1,1)
    data_new1 = np.concatenate((V2,P2,W2,Water2,),axis=1)
    data_new1 = transfer1.fit_transform(data_new1)        
    data_new.append(data_new1.tolist())
data_new=np.array(data_new)
# =========================================================================================================
#4.数据可视化
#数据平滑
from scipy import signal
def smooth(a, nx, ny):
    kernel = np.ones((nx,ny)) * 1 / (nx * ny)
    r = signal.convolve2d(a, kernel, mode = 'same')
    return r

#颜色条设置
import cmasher as cmr
cmapv = cmr.get_sub_cmap('cmr.fusion_r', 0, 1,N=64 )#N=32
cmapp = cmr.get_sub_cmap('cmr.copper', 0, 1,N=64)#N=32
cmapw = cmr.get_sub_cmap('cmr.viola', 0, 1,)#N=32
cmapwater = cmr.get_sub_cmap('cmr.ocean', 0, 1,)#N=32
cmap = cmr.get_sub_cmap('cmr.seasons', 0, 1,N=64)#
#'RdBu_r'(红蓝白),'RdBu'（红蓝反向）,'rainbow'(红蓝)
"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
论文内容
"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
#######################################相空间重构，不完全可观测，时延嵌入#################
"""
论文内容1
"""
##########################################降维方法的应用与评价##########################
"""
论文内容2
"""
from PCAWZH2024 import PCAWZH
from SVDWZH2024 import SVDWZH
from ICAWZH2024 import ICAWZH
from NMFWZH2024 import NMFWZH
from KPCAWZH2024 import KPCAWZH
from ISOMAPWZH2024 import ISOMAPWZH
from LLEWZH2024 import LLEWZH
from LEMWZH2024 import LEMWZH
from sklearn.metrics import mutual_info_score
from sklearn.preprocessing import MinMaxScaler

#X=uxreshape#时间
X=U_star.T#时间
#X=X.T#空间
"""
线性降维PCA
"""
# from sklearn.preprocessing import StandardScaler#标准化函数
# transfer=StandardScaler()#标准化函数Z-score标准化方法
# X = transfer.fit_transform(X) 
#K的选择
rand_scores_pcas=[]
nmi_scores_pcas=[]
fmi_scores_pcas=[]
silhouette_scores_pcas=[]
for k in range(5,75,5):
    print(k)
    pca = PCAWZH(r_max=10, r_step=1,n_cluster=k)
    mode_pca,scaler_mode_pca= pca.fit_transform(X)
    reconstructed_pca,reconstructed_pcas,err_pca,err_rmse_pca,low_dims_pca,high_dims_pca,all_structure_corrs_pca,all_structure_mi_scores_pca,all_structure_dtws_pca,all_structure_trustworthinesss_pca,rand_scores_pca,nmi_scores_pca,fmi_scores_pca,silhouette_scores_pca,class_scores_pca,transition_matrixs_pca,err_tpms_pca,err_tpm_values_pca,r2s_pca,mses_pca,sampens_pca,hursts_pca,lyaps_pca= pca.inverse_transform(X)
    rand_scores_pcas.append(rand_scores_pca)
    nmi_scores_pcas.append(nmi_scores_pca)
    fmi_scores_pcas.append(fmi_scores_pca)
    silhouette_scores_pcas.append(silhouette_scores_pca)
rand_scores_pcas=np.array(rand_scores_pcas)
nmi_scores_pcas=np.array(nmi_scores_pcas)
fmi_scores_pcas=np.array(fmi_scores_pcas)
silhouette_scores_pcas=np.array(silhouette_scores_pcas)

pca = PCAWZH(r_max=10, r_step=1,n_cluster=15)
mode_pca,scaler_mode_pca= pca.fit_transform(X)
reconstructed_pca,reconstructed_pcas,err_pca,err_rmse_pca,low_dims_pca,high_dims_pca,all_structure_corrs_pca,all_structure_mi_scores_pca,all_structure_dtws_pca,all_structure_trustworthinesss_pca,rand_scores_pca,nmi_scores_pca,fmi_scores_pca,silhouette_scores_pca,class_scores_pca,transition_matrixs_pca,err_tpms_pca,err_tpm_values_pca,r2s_pca,mses_pca,sampens_pca,hursts_pca,lyaps_pca= pca.inverse_transform(X)

"""
线性降维SVD
"""
#K的选择
rand_scores_svds=[]
nmi_scores_svds=[]
fmi_scores_svds=[]
silhouette_scores_svds=[]
for k in range(5,75,5):
    print(k)
    svd = SVDWZH(r_max=10, r_step=1,n_cluster=k)
    mode_svd,scaler_mode_svd= svd.fit_transform(X)
    reconstructed_svd,reconstructed_svds,err_svd,err_rmse_svd,low_dims_svd,high_dims_svd,all_structure_corrs_svd,all_structure_mi_scores_svd,all_structure_dtws_svd,all_structure_trustworthinesss_svd,rand_scores_svd,nmi_scores_svd,fmi_scores_svd,silhouette_scores_svd,class_scores_svd,transition_matrixs_svd,err_tpms_svd,err_tpm_values_svd,r2s_svd,mses_svd,sampens_svd,hursts_svd,lyaps_svd= svd.inverse_transform(X)
    rand_scores_svds.append(rand_scores_svd)
    nmi_scores_svds.append(nmi_scores_svd)
    fmi_scores_svds.append(fmi_scores_svd)
    silhouette_scores_svds.append(silhouette_scores_svd)
rand_scores_svds=np.array(rand_scores_svds)
nmi_scores_svds=np.array(nmi_scores_svds)
fmi_scores_svds=np.array(fmi_scores_svds)
silhouette_scores_svds=np.array(silhouette_scores_svds)

svd = SVDWZH(r_max=10, r_step=1,n_cluster=15)
mode_svd,scaler_mode_svd= svd.fit_transform(X)
reconstructed_svd,reconstructed_svds,err_svd,err_rmse_svd,low_dims_svd,high_dims_svd,all_structure_corrs_svd,all_structure_mi_scores_svd,all_structure_dtws_svd,all_structure_trustworthinesss_svd,rand_scores_svd,nmi_scores_svd,fmi_scores_svd,silhouette_scores_svd,class_scores_svd,transition_matrixs_svd,err_tpms_svd,err_tpm_values_svd,r2s_svd,mses_svd,sampens_svd,hursts_svd,lyaps_svd= svd.inverse_transform(X)

"""
线性降维ICA
"""
#K的选择
rand_scores_icas=[]
nmi_scores_icas=[]
fmi_scores_icas=[]
silhouette_scores_icas=[]
for k in range(5,75,5):
    print(k)
    ica = ICAWZH(r_max=10, r_step=1,n_cluster=k)
    mode_ica,scaler_mode_ica= ica.fit_transform(X)
    reconstructed_ica,reconstructed_icas,err_ica,err_rmse_ica,low_dims_ica,high_dims_ica,all_structure_corrs_ica,all_structure_mi_scores_ica,all_structure_dtws_ica,all_structure_trustworthinesss_ica,rand_scores_ica,nmi_scores_ica,fmi_scores_ica,silhouette_scores_ica,class_scores_ica,transition_matrixs_ica,err_tpms_ica,err_tpm_values_ica,r2s_ica,mses_ica,sampens_ica,hursts_ica,lyaps_ica= ica.inverse_transform(X)
    rand_scores_icas.append(rand_scores_ica)
    nmi_scores_icas.append(nmi_scores_ica)
    fmi_scores_icas.append(fmi_scores_ica)
    silhouette_scores_icas.append(silhouette_scores_ica)
rand_scores_icas=np.array(rand_scores_icas)
nmi_scores_icas=np.array(nmi_scores_icas)
fmi_scores_icas=np.array(fmi_scores_icas)
silhouette_scores_icas=np.array(silhouette_scores_icas)

ica = ICAWZH(r_max=10, r_step=1,n_cluster=15)
mode_ica,scaler_mode_ica= ica.fit_transform(X)
reconstructed_ica,reconstructed_icas,err_ica,err_rmse_ica,low_dims_ica,high_dims_ica,all_structure_corrs_ica,all_structure_mi_scores_ica,all_structure_dtws_ica,all_structure_trustworthinesss_ica,rand_scores_ica,nmi_scores_ica,fmi_scores_ica,silhouette_scores_ica,class_scores_ica,transition_matrixs_ica,err_tpms_ica,err_tpm_values_ica,r2s_ica,mses_ica,sampens_ica,hursts_ica,lyaps_ica= ica.inverse_transform(X)

"""
线性降维NMF
"""
#K的选择
from sklearn.preprocessing import MinMaxScaler#标准化函数
transfer=MinMaxScaler()#标准化函数Z-score标准化方法
X_NMF = transfer.fit_transform(X) 

rand_scores_nmfs=[]
nmi_scores_nmfs=[]
fmi_scores_nmfs=[]
silhouette_scores_nmfs=[]
for k in range(5,75,5):
    print(k)
    nmf = NMFWZH(r_max=10, r_step=1,n_cluster=k)
    mode_nmf,scaler_mode_nmf= nmf.fit_transform(X_NMF)
    reconstructed_nmf,reconstructed_nmfs,err_nmf,err_rmse_nmf,low_dims_nmf,high_dims_nmf,all_structure_corrs_nmf,all_structure_mi_scores_nmf,all_structure_dtws_nmf,all_structure_trustworthinesss_nmf,rand_scores_nmf,nmi_scores_nmf,fmi_scores_nmf,silhouette_scores_nmf,class_scores_nmf,transition_matrixs_nmf,err_tpms_nmf,err_tpm_values_nmf,r2s_nmf,mses_nmf,sampens_nmf,hursts_nmf,lyaps_nmf= nmf.inverse_transform(X_NMF)
    rand_scores_nmfs.append(rand_scores_nmf)
    nmi_scores_nmfs.append(nmi_scores_nmf)
    fmi_scores_nmfs.append(fmi_scores_nmf)
    silhouette_scores_nmfs.append(silhouette_scores_nmf)
rand_scores_nmfs=np.array(rand_scores_nmfs)
nmi_scores_nmfs=np.array(nmi_scores_nmfs)
fmi_scores_nmfs=np.array(fmi_scores_nmfs)
silhouette_scores_nmfs=np.array(silhouette_scores_nmfs)

nmf = NMFWZH(r_max=10, r_step=1,n_cluster=15)
mode_nmf,scaler_mode_nmf= nmf.fit_transform(X_NMF)
reconstructed_nmf,reconstructed_nmfs,err_nmf,err_rmse_nmf,low_dims_nmf,high_dims_nmf,all_structure_corrs_nmf,all_structure_mi_scores_nmf,all_structure_dtws_nmf,all_structure_trustworthinesss_nmf,rand_scores_nmf,nmi_scores_nmf,fmi_scores_nmf,silhouette_scores_nmf,class_scores_nmf,transition_matrixs_nmf,err_tpms_nmf,err_tpm_values_nmf,r2s_nmf,mses_nmf,sampens_nmf,hursts_nmf,lyaps_nmf= nmf.inverse_transform(X_NMF)

"""
非线性降维KPCA
"""
#参数gamma,alpha的选择
from sklearn.decomposition import KernelPCA
err_recs_kpca = []
gammas = [0.0001, 0.001, 0.01, 0.1, 0.5, 1, 5, 10, 15, 20, 30, 40, 50, 70, 100]
alphas = [0.0001, 0.001, 0.01, 0.1, 0.5, 1]
best_error = float('inf')
best_params = {'gamma': None, 'alpha': None}
for gamma in gammas:
    for alpha in alphas:
        kpca = KernelPCA(n_components=10, kernel='rbf', fit_inverse_transform=True,
                         gamma=gamma, alpha=alpha, n_jobs=-1)
        X_kpca = kpca.fit(X)
        X_fit_transform = X_kpca.fit_transform(X)
        X_reconstructed = kpca.inverse_transform(X_fit_transform)
        err_kpca = np.linalg.norm(X - X_reconstructed) / np.linalg.norm(X)
        if err_kpca < best_error:
            best_error = err_kpca
            best_params['gamma'] = gamma
            best_params['alpha'] = alpha
        err_recs_kpca.append(err_kpca)
err_recs_kpca=np.array(err_recs_kpca).reshape(15,6)              
print(f"Best gamma: {best_params['gamma']}")
print(f"Best alpha: {best_params['alpha']}")
print(f"Best reconstruction error: {best_error}")

#参数k的选择
rand_scores_kpcas=[]
nmi_scores_kpcas=[]
fmi_scores_kpcas=[]
silhouette_scores_kpcas=[]
for k in range(5,75,5):
    print(k)
    kpca = KPCAWZH(r_max=10, r_step=1, kernel_fcn='rbf', gamma=0.1, alpha=0.0001,n_cluster=k)#时间
    mode_kpca,scaler_mode_kpca= kpca.fit_transform(X)
    reconstructed_kpca,reconstructed_kpcas,err_kpca,err_rmse_kpca,low_dims_kpca,high_dims_kpca,all_structure_corrs_kpca,all_structure_mi_scores_kpca,all_structure_dtws_kpca,all_structure_trustworthinesss_kpca,rand_scores_kpca,nmi_scores_kpca,fmi_scores_kpca,silhouette_scores_kpca,class_scores_kpca,transition_matrixs_kpca,err_tpms_kpca,err_tpm_values_kpca,r2s_kpca,mses_kpca,sampens_kpca,hursts_kpca,lyaps_kpca= kpca.inverse_transform(X)
    rand_scores_kpcas.append(rand_scores_kpca)
    nmi_scores_kpcas.append(nmi_scores_kpca)
    fmi_scores_kpcas.append(fmi_scores_kpca)
    silhouette_scores_kpcas.append(silhouette_scores_kpca)
rand_scores_kpcas=np.array(rand_scores_kpcas)
nmi_scores_kpcas=np.array(nmi_scores_kpcas)
fmi_scores_kpcas=np.array(fmi_scores_kpcas)
silhouette_scores_kpcas=np.array(silhouette_scores_kpcas)

kpca = KPCAWZH(r_max=10, r_step=1, kernel_fcn='rbf', gamma=0.1, alpha=0.0001,n_cluster=15)#时间
mode_kpca,scaler_mode_kpca= kpca.fit_transform(X)
reconstructed_kpca,reconstructed_kpcas,err_kpca,err_rmse_kpca,low_dims_kpca,high_dims_kpca,all_structure_corrs_kpca,all_structure_mi_scores_kpca,all_structure_dtws_kpca,all_structure_trustworthinesss_kpca,rand_scores_kpca,nmi_scores_kpca,fmi_scores_kpca,silhouette_scores_kpca,class_scores_kpca,transition_matrixs_kpca,err_tpms_kpca,err_tpm_values_kpca,r2s_kpca,mses_kpca,sampens_kpca,hursts_kpca,lyaps_kpca= kpca.inverse_transform(X)

"""
非线性降维ISOMAP
"""
#参数gamma,alpha的选择
knns = range(5, 75, 5) 
err_recs_isomap = []
best_error = float('inf')
best_params = {'knn': None, 'reg': None}
for knn in knns:
    isomap = Isomap(n_components=10, n_neighbors=knn, n_jobs=16)  # Use knn and reg correctly
    Y = isomap.fit_transform(X)
    W = barycenter_kneighbors_graph(Y, n_neighbors=knn, reg=1e-9, n_jobs=16)  # Correct knn variable used
    X_reconstructed = W @ X
    err_isomap = np.linalg.norm(X - X_reconstructed) / np.linalg.norm(X)
    if err_isomap < best_error:
        best_error = err_isomap
        best_params['knn'] = knn
    err_recs_isomap.append(err_isomap)
err_recs_isomap = np.array(err_recs_isomap).reshape(len(knns))
print(f"Best knn: {best_params['knn']}")
print(f"Best reconstruction error: {best_error}")

#参数k的选择
rand_scores_isomaps=[]
nmi_scores_isomaps=[]
fmi_scores_isomaps=[]
silhouette_scores_isomaps=[]
for k in range(5,75,5):
    print(k)
    isomap = ISOMAPWZH(r_max=10, r_step=1,k_NN=20, n_cluster=k)#时间
    mode_isomap,scaler_mode_isomap= isomap.fit_transform(X)
    reconstructed_isomap,reconstructed_isomaps,err_isomap,err_rmse_isomap,low_dims_isomap,high_dims_isomap,all_structure_corrs_isomap,all_structure_mi_scores_isomap,all_structure_dtws_isomap,all_structure_trustworthinesss_isomap,rand_scores_isomap,nmi_scores_isomap,fmi_scores_isomap,silhouette_scores_isomap,class_scores_isomap,transition_matrixs_isomap,err_tpms_isomap,err_tpm_values_isomap,r2s_isomap,mses_isomap,sampens_isomap,hursts_isomap,lyaps_isomap= isomap.inverse_transform(X)
    rand_scores_isomaps.append(rand_scores_isomap)
    nmi_scores_isomaps.append(nmi_scores_isomap)
    fmi_scores_isomaps.append(fmi_scores_isomap)
    silhouette_scores_isomaps.append(silhouette_scores_isomap)
rand_scores_isomaps=np.array(rand_scores_isomaps)
nmi_scores_isomaps=np.array(nmi_scores_isomaps)
fmi_scores_isomaps=np.array(fmi_scores_isomaps)
silhouette_scores_isomaps=np.array(silhouette_scores_isomaps)

isomap = ISOMAPWZH(r_max=10, r_step=1,k_NN=20, n_cluster=15)#时间
mode_isomap,scaler_mode_isomap= isomap.fit_transform(X)
reconstructed_isomap,reconstructed_isomaps,err_isomap,err_rmse_isomap,low_dims_isomap,high_dims_isomap,all_structure_corrs_isomap,all_structure_mi_scores_isomap,all_structure_dtws_isomap,all_structure_trustworthinesss_isomap,rand_scores_isomap,nmi_scores_isomap,fmi_scores_isomap,silhouette_scores_isomap,class_scores_isomap,transition_matrixs_isomap,err_tpms_isomap,err_tpm_values_isomap,r2s_isomap,mses_isomap,sampens_isomap,hursts_isomap,lyaps_isomap= isomap.inverse_transform(X)

"""
非线性降维LLE
"""

#参数gamma,alpha的选择
knns = range(5, 75, 5)  # Correcting the knns variable
regs = [1e-15,1e-12,1e-9, 1e-6, 1e-3, 1e-2, 1e-1, 1]
err_recs_lle = []
best_error = float('inf')
best_params = {'knn': None, 'reg': None}
for knn in knns:
    for reg in regs:
        lle = LocallyLinearEmbedding(n_components=10, n_neighbors=knn, reg=reg, n_jobs=16)  # Use knn and reg correctly
        Y = lle.fit_transform(X)
        W = barycenter_kneighbors_graph(Y, n_neighbors=knn, reg=reg, n_jobs=16)  # Correct knn variable used
        X_reconstructed = W @ X
        err_lle = np.linalg.norm(X - X_reconstructed) / np.linalg.norm(X)
        if err_lle < best_error:
            best_error = err_lle
            best_params['knn'] = knn
            best_params['reg'] = reg
        err_recs_lle.append(err_lle)
err_recs_lle = np.array(err_recs_lle).reshape(len(knns), len(regs))
print(f"Best knn: {best_params['knn']}")
print(f"Best reg: {best_params['reg']}")
print(f"Best reconstruction error: {best_error}")

#参数k的选择
rand_scores_lles=[]
nmi_scores_lles=[]
fmi_scores_lles=[]
silhouette_scores_lles=[]
for k in range(5,75,5):
    print(k)
    lle = LLEWZH(r_max=10, r_step=1,k_NN=15,reg=1e-9, n_cluster=k)#时间
    mode_lle,scaler_mode_lle= lle.fit_transform(X)
    reconstructed_lle,reconstructed_lles,err_lle,err_rmse_lle,low_dims_lle,high_dims_lle,all_structure_corrs_lle,all_structure_mi_scores_lle,all_structure_dtws_lle,all_structure_trustworthinesss_lle,rand_scores_lle,nmi_scores_lle,fmi_scores_lle,silhouette_scores_lle,class_scores_lle,transition_matrixs_lle,err_tpms_lle,err_tpm_values_lle,r2s_lle,mses_lle,sampens_lle,hursts_lle,lyaps_lle= lle.inverse_transform(X)
    rand_scores_lles.append(rand_scores_lle)
    nmi_scores_lles.append(nmi_scores_lle)
    fmi_scores_lles.append(fmi_scores_lle)
    silhouette_scores_lles.append(silhouette_scores_lle)
rand_scores_lles=np.array(rand_scores_lles)
nmi_scores_lles=np.array(nmi_scores_lles)
fmi_scores_lles=np.array(fmi_scores_lles)
silhouette_scores_lles=np.array(silhouette_scores_lles)

lle = LLEWZH(r_max=10, r_step=1,k_NN=15,reg=1e-9, n_cluster=15)#时间
mode_lle,scaler_mode_lle= lle.fit_transform(X)
reconstructed_lle,reconstructed_lles,err_lle,err_rmse_lle,low_dims_lle,high_dims_lle,all_structure_corrs_lle,all_structure_mi_scores_lle,all_structure_dtws_lle,all_structure_trustworthinesss_lle,rand_scores_lle,nmi_scores_lle,fmi_scores_lle,silhouette_scores_lle,class_scores_lle,transition_matrixs_lle,err_tpms_lle,err_tpm_values_lle,r2s_lle,mses_lle,sampens_lle,hursts_lle,lyaps_lle= lle.inverse_transform(X)

"""
非线性降维LEM
"""

#参数gamma,alpha的选择
knns = range(5, 75, 5)
regs = [1e-15, 1e-12, 1e-9, 1e-6, 1e-3, 1e-2, 1e-1, 1]
gammas = [0.0001, 0.001, 0.01, 0.1, 0.5, 1, 5, 10, 15, 20, 30, 40, 50, 70, 100]
err_recs_lle = []
best_error = float('inf')
best_params = {'knn': None, 'reg': None, 'gamma': None}
for knn in knns:
    for reg in regs:
        for gamma in gammas:
            affinity_knn = kneighbors_graph(X, knn-1, include_self=False)
            affinity_knn = np.ceil(0.5 * (affinity_knn + affinity_knn.T).todense())  # Symmetrize and convert to dense
            affinity_rbf = rbf_kernel(X, gamma=gamma)
            affinity_mat = np.multiply(affinity_knn, affinity_rbf)  
            affinity_mat = affinity_rbf
            lem = SpectralEmbedding(n_components=10, affinity='precomputed', random_state=42, n_jobs=16)
            Y = lem.fit_transform(affinity_mat)  # Correct 'lem' object used
            W = barycenter_kneighbors_graph(Y, n_neighbors=knn, reg=reg, n_jobs=16)  # Use correct knn and reg
            X_reconstructed = W @ X
            err_lle = np.linalg.norm(X - X_reconstructed) / np.linalg.norm(X)
            if err_lle < best_error:
                best_error = err_lle
                best_params['knn'] = knn
                best_params['reg'] = reg
                best_params['gamma'] = gamma 
            err_recs_lle.append(err_lle)
err_recs_lle = np.array(err_recs_lle).reshape(len(knns), len(regs), len(gammas))
print(f"Best knn: {best_params['knn']}")
print(f"Best reg: {best_params['reg']}")
print(f"Best gamma: {best_params['gamma']}")
print(f"Best reconstruction error: {best_error}")

#参数k的选择
rand_scores_lems=[]
nmi_scores_lems=[]
fmi_scores_lems=[]
silhouette_scores_lems=[]
for k in range(5,75,5):
    print(k)
    lem = LEMWZH(r_max=10, r_step=1,k_NN=20,reg=1e-15, gamma=0.01, n_cluster=k)#时间
    mode_lem,scaler_mode_lem= lem.fit_transform(X)
    reconstructed_lem,reconstructed_lems,err_lem,err_rmse_lem,low_dims_lem,high_dims_lem,all_structure_corrs_lem,all_structure_mi_scores_lem,all_structure_dtws_lem,all_structure_trustworthinesss_lem,rand_scores_lem,nmi_scores_lem,fmi_scores_lem,silhouette_scores_lem,class_scores_lem,transition_matrixs_lem,err_tpms_lem,err_tpm_values_lem,r2s_lem,mses_lem,sampens_lem,hursts_lem,lyaps_lem= lem.inverse_transform(X)
    rand_scores_lems.append(rand_scores_lem)
    nmi_scores_lems.append(nmi_scores_lem)
    fmi_scores_lems.append(fmi_scores_lem)
    silhouette_scores_lems.append(silhouette_scores_lem)
rand_scores_lems=np.array(rand_scores_lems)
nmi_scores_lems=np.array(nmi_scores_lems)
fmi_scores_lems=np.array(fmi_scores_lems)
silhouette_scores_lems=np.array(silhouette_scores_lems)

lem = LEMWZH(r_max=10, r_step=1,k_NN=20,reg=1e-15, gamma=0.01,  n_cluster=15)#时间
mode_lem,scaler_mode_lem= lem.fit_transform(X)
reconstructed_lem,reconstructed_lems,err_lem,err_rmse_lem,low_dims_lem,high_dims_lem,all_structure_corrs_lem,all_structure_mi_scores_lem,all_structure_dtws_lem,all_structure_trustworthinesss_lem,rand_scores_lem,nmi_scores_lem,fmi_scores_lem,silhouette_scores_lem,class_scores_lem,transition_matrixs_lem,err_tpms_lem,err_tpm_values_lem,r2s_lem,mses_lem,sampens_lem,hursts_lem,lyaps_lem= lem.inverse_transform(X)

from AEWZH2024 import AEWZH
# #参数k的选择
# rand_scores_aes=[]
# nmi_scores_aes=[]
# fmi_scores_aes=[]
# silhouette_scores_aes=[]
# for k in range(5,75,5):
#     print(k)
#     ae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='AE',input_dim=4000,n_cluster=k)
#     mode_ae,scaler_mode_ae,reconstructed_ae,reconstructed_aes,err_ae,err_rmse_ae,low_dims_ae,high_dims_ae,all_structure_corrs_ae,all_structure_mi_scores_ae,all_structure_dtws_ae,all_structure_trustworthinesss_ae,rand_scores_ae,nmi_scores_ae,fmi_scores_ae,silhouette_scores_ae,class_scores_ae,transition_matrixs_ae,err_tpms_ae,err_tpm_values_ae,r2s_ae,mses_ae,sampens_ae,hursts_ae,lyaps_ae= ae.fit_transform(X)
#     rand_scores_aes.append(rand_scores_ae)
#     nmi_scores_aes.append(nmi_scores_ae)
#     fmi_scores_aes.append(fmi_scores_ae)
#     silhouette_scores_aes.append(silhouette_scores_ae)
# rand_scores_aes=np.array(rand_scores_aes)
# nmi_scores_aes=np.array(nmi_scores_aes)
# fmi_scores_aes=np.array(fmi_scores_aes)
# silhouette_scores_aes=np.array(silhouette_scores_aes)
ae = AEWZH(r_max=8, r_step=1, batch_size=201, lr=1e-3,model_type='AE',input_dim=30189, n_cluster=15)
mode_ae,scaler_mode_ae,reconstructed_ae,reconstructed_aes,err_ae,err_rmse_ae,low_dims_ae,high_dims_ae,all_structure_corrs_ae,all_structure_mi_scores_ae,all_structure_dtws_ae,all_structure_trustworthinesss_ae,rand_scores_ae,nmi_scores_ae,fmi_scores_ae,silhouette_scores_ae,class_scores_ae,transition_matrixs_ae,err_tpms_ae,err_tpm_values_ae,r2s_ae,mses_ae,sampens_ae,hursts_ae,lyaps_ae,hidden_layers_encoder_PCAae,hidden_layers_decoder_PCAae= ae.fit_transform(X)

# """
# 非线性降维VAE
# """
# #参数k的选择
# # rand_scores_vaes=[]
# # nmi_scores_vaes=[]
# # fmi_scores_vaes=[]
# # silhouette_scores_vaes=[]
# # for k in range(5,75,5):
# #     print(k)
# #     vae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='VAE',input_dim=4000,n_cluster=k)
# #     mode_vae,scaler_mode_vae,reconstructed_vae,reconstructed_vaes,err_vae,err_rmse_vae,low_dims_vae,high_dims_vae,all_structure_corrs_vae,all_structure_mi_scores_vae,all_structure_dtws_vae,all_structure_trustworthinesss_vae,rand_scores_vae,nmi_scores_vae,fmi_scores_vae,silhouette_scores_vae,class_scores_vae,transition_matrixs_vae,err_tpms_vae,err_tpm_values_vae,r2s_vae,mses_vae,sampens_vae,hursts_vae,lyaps_vae= vae.fit_transform(X)
# #     rand_scores_vaes.append(rand_scores_vae)
# #     nmi_scores_vaes.append(nmi_scores_vae)
# #     fmi_scores_vaes.append(fmi_scores_vae)
# #     silhouette_scores_vaes.append(silhouette_scores_vae)
# # rand_scores_vaes=np.array(rand_scores_vaes)
# # nmi_scores_vaes=np.array(nmi_scores_vaes)
# # fmi_scores_vaes=np.array(fmi_scores_vaes)
# # silhouette_scores_vaes=np.array(silhouette_scores_vaes)

vae = AEWZH(r_max=8, r_step=1, batch_size=201, lr=1e-3,model_type='VAE',input_dim=30189, n_cluster=15)
mode_vae,scaler_mode_vae,reconstructed_vae,reconstructed_vaes,err_vae,err_rmse_vae,low_dims_vae,high_dims_vae,all_structure_corrs_vae,all_structure_mi_scores_vae,all_structure_dtws_vae,all_structure_trustworthinesss_vae,rand_scores_vae,nmi_scores_vae,fmi_scores_vae,silhouette_scores_vae,class_scores_vae,transition_matrixs_vae,err_tpms_vae,err_tpm_values_vae,r2s_vae,mses_vae,sampens_vae,hursts_vae,lyaps_vae= vae.fit_transform(X)

# """
# 非线性降维KAE
# """
# #参数k的选择
# # rand_scores_kaes=[]
# # nmi_scores_kaes=[]
# # fmi_scores_kaes=[]
# # silhouette_scores_kaes=[]
# # for k in range(5,75,5):
# #     print(k)
# #     kae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='KAE',input_dim=4000,n_cluster=k)
# #     mode_kae,scaler_mode_kae,reconstructed_kae,reconstructed_kaes,err_kae,err_rmse_kae,low_dims_kae,high_dims_kae,all_structure_corrs_kae,all_structure_mi_scores_kae,all_structure_dtws_kae,all_structure_trustworthinesss_kae,rand_scores_kae,nmi_scores_kae,fmi_scores_kae,silhouette_scores_kae,class_scores_kae,transition_matrixs_kae,err_tpms_kae,err_tpm_values_kae,r2s_kae,mses_kae,sampens_kae,hursts_kae,lyaps_kae= kae.fit_transform(X)
# #     rand_scores_kaes.append(rand_scores_kae)
# #     nmi_scores_kaes.append(nmi_scores_kae)
# #     fmi_scores_kaes.append(fmi_scores_kae)
# #     silhouette_scores_kaes.append(silhouette_scores_kae)
# # rand_scores_kaes=np.array(rand_scores_kaes)
# # nmi_scores_kaes=np.array(nmi_scores_kaes)
# # fmi_scores_kaes=np.array(fmi_scores_kaes)
# # silhouette_scores_kaes=np.array(silhouette_scores_kaes)

# kae = AEWZH(r_max=10, r_step=1, batch_size=401, lr=1e-3,model_type='KAE',input_dim=4000, n_cluster=15)
# mode_kae,scaler_mode_kae,reconstructed_kae,reconstructed_kaes,err_kae,err_rmse_kae,low_dims_kae,high_dims_kae,all_structure_corrs_kae,all_structure_mi_scores_kae,all_structure_dtws_kae,all_structure_trustworthinesss_kae,rand_scores_kae,nmi_scores_kae,fmi_scores_kae,silhouette_scores_kae,class_scores_kae,transition_matrixs_kae,err_tpms_kae,err_tpm_values_kae,r2s_kae,mses_kae,sampens_kae,hursts_kae,lyaps_kae= kae.fit_transform(X)

# """
# 非线性降维KVAE
# """
# #参数k的选择
# # rand_scores_kvaes=[]
# # nmi_scores_kvaes=[]
# # fmi_scores_kvaes=[]
# # silhouette_scores_kvaes=[]
# # for k in range(5,75,5):
# #     print(k)
# #     kvae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='KVAE',input_dim=4000,n_cluster=k)
# #     mode_kvae,scaler_mode_kvae,reconstructed_kvae,reconstructed_kvaes,err_kvae,err_rmse_kvae,low_dims_kvae,high_dims_kvae,all_structure_corrs_kvae,all_structure_mi_scores_kvae,all_structure_dtws_kvae,all_structure_trustworthinesss_kvae,rand_scores_kvae,nmi_scores_kvae,fmi_scores_kvae,silhouette_scores_kvae,class_scores_kvae,transition_matrixs_kvae,err_tpms_kvae,err_tpm_values_kvae,r2s_kvae,mses_kvae,sampens_kvae,hursts_kvae,lyaps_kvae= kvae.fit_transform(X)
# #     rand_scores_kvaes.append(rand_scores_kvae)
# #     nmi_scores_kvaes.append(nmi_scores_kvae)
# #     fmi_scores_kvaes.append(fmi_scores_kvae)
# #     silhouette_scores_kvaes.append(silhouette_scores_kvae)
# # rand_scores_kvaes=np.array(rand_scores_kvaes)
# # nmi_scores_kvaes=np.array(nmi_scores_kvaes)
# # fmi_scores_kvaes=np.array(fmi_scores_kvaes)
# # silhouette_scores_kvaes=np.array(silhouette_scores_kvaes)
# kvae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='KVAE',input_dim=4000, n_cluster=15)
# mode_kvae,scaler_mode_kvae,reconstructed_kvae,reconstructed_kvaes,err_kvae,err_rmse_kvae,low_dims_kvae,high_dims_kvae,all_structure_corrs_kvae,all_structure_mi_scores_kvae,all_structure_dtws_kvae,all_structure_trustworthinesss_kvae,rand_scores_kvae,nmi_scores_kvae,fmi_scores_kvae,silhouette_scores_kvae,class_scores_kvae,transition_matrixs_kvae,err_tpms_kvae,err_tpm_values_kvae,r2s_kvae,mses_kvae,sampens_kvae,hursts_kvae,lyaps_kvae= kvae.fit_transform(X)

# =============================================================================
# 模块化函数使用示例
# =============================================================================

def main_analysis_example():
    """
    使用模块化函数进行数据分析的示例

    这个函数展示了如何使用上面定义的模块化函数来替代原始的内联代码
    """
    print("=" * 80)
    print("开始使用模块化函数进行圆柱绕流数据分析")
    print("=" * 80)

    try:
        # 1. 加载原始流场数据
        print("\n步骤1: 加载原始流场数据")
        cylinder_data_raw = load_cylinder_flow_data_A()

        # 2. 处理流场数据（稀疏化等）
        print("\n步骤2: 处理流场数据")
        cylinder_flow_data = process_cylinder_flow_data_A(cylinder_data_raw['raw_data'], sparse=4)

        # 3. 可视化不同场变量的快照
        print("\n步骤3: 可视化流场快照")

        # 可视化涡量场
        visualize_cylinder_flow_snapshot(cylinder_flow_data, time_index=0, field='W')

        # 可视化速度场（可选）
        # visualize_cylinder_flow_snapshot(cylinder_flow_data, time_index=0, field='U')
        # visualize_cylinder_flow_snapshot(cylinder_flow_data, time_index=0, field='V')

        # 4. 加载力系数数据
        print("\n步骤4: 加载力系数数据")
        cylinder_force_data = load_cylinder_force_data_A()

        # 5. 可视化力系数
        print("\n步骤5: 可视化力系数")
        visualize_force_coefficients(cylinder_force_data)

        # 6. 返回处理后的数据供后续分析使用
        print("\n步骤6: 数据准备完成，可用于降维分析")

        return {
            'flow_data': cylinder_flow_data,
            'force_data': cylinder_force_data,
            'raw_data': cylinder_data_raw
        }

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        print("请检查数据路径和文件是否存在")
        return None

def get_data_for_dimensionality_reduction(flow_data, field='W'):
    """
    准备用于降维分析的数据矩阵

    Parameters:
        flow_data (dict): 流场数据字典
        field (str): 要分析的场变量，默认为'W'（涡量）

    Returns:
        np.ndarray: 用于降维的数据矩阵，形状为 (时间步, 空间点)
    """
    print(f"正在准备 {field} 场的降维数据...")

    if field == 'W':
        data_matrix = flow_data['W'].reshape(flow_data['W'].shape[0], -1)
        field_name = '涡量'
    elif field == 'U':
        data_matrix = flow_data['U'].reshape(flow_data['U'].shape[0], -1)
        field_name = 'x方向速度'
    elif field == 'V':
        data_matrix = flow_data['V'].reshape(flow_data['V'].shape[0], -1)
        field_name = 'y方向速度'
    elif field == 'P':
        data_matrix = flow_data['P'].reshape(flow_data['P'].shape[0], -1)
        field_name = '压力'
    else:
        data_matrix = flow_data['W'].reshape(flow_data['W'].shape[0], -1)
        field_name = '涡量'

    print(f"{field_name}场数据矩阵形状: {data_matrix.shape}")
    print(f"时间步数: {data_matrix.shape[0]}, 空间点数: {data_matrix.shape[1]}")

    return data_matrix

def comprehensive_data_analysis_example():
    """
    综合数据分析示例 - 展示如何使用所有模块化函数
    """
    print("=" * 80)
    print("综合数据分析示例")
    print("=" * 80)

    analysis_results = {}

    # 1. 圆柱绕流数据分析
    print("\n1. 圆柱绕流数据分析")
    print("-" * 40)
    try:
        cylinder_results = main_analysis_example()
        if cylinder_results:
            analysis_results['cylinder'] = cylinder_results
            print("✓ 圆柱绕流数据分析完成")
    except Exception as e:
        print(f"✗ 圆柱绕流数据分析失败: {e}")

    # 2. 翼型DNS数据分析
    print("\n2. 翼型DNS数据分析")
    print("-" * 40)
    try:
        # 加载翼型数据配置
        airfoil_config = load_airfoil_dns_data(base_angle=30, freq_str='0p2', sparse_value=2)
        if airfoil_config:
            # 加载流场数据
            airfoil_flow = load_airfoil_flow_data(airfoil_config)
            if airfoil_flow:
                # 可视化翼型流场
                visualize_airfoil_flow(airfoil_flow, data_type='dynamic', field='vorticity', time_index=0)
                analysis_results['airfoil'] = airfoil_flow
                print("✓ 翼型DNS数据分析完成")
    except Exception as e:
        print(f"✗ 翼型DNS数据分析失败: {e}")

    # 3. PIV数据分析
    print("\n3. PIV数据分析")
    print("-" * 40)
    try:
        # 加载PIV数据（采样以节省时间）
        piv_data = load_cylinder_piv_data(sampling_step=500)
        if piv_data:
            # 可视化PIV数据
            visualize_piv_data(piv_data, field='magnitude', time_index=0)
            analysis_results['piv'] = piv_data
            print("✓ PIV数据分析完成")
    except Exception as e:
        print(f"✗ PIV数据分析失败: {e}")

    # 4. 粒子数据分析（可选，因为文件较多）
    print("\n4. 粒子数据分析（可选）")
    print("-" * 40)
    load_particle = input("是否加载粒子数据？(y/n，注意：可能需要较长时间): ").lower().strip()
    if load_particle == 'y' or load_particle == 'yes':
        try:
            # 限制文件数量以节省时间
            particle_data = load_particle_data(max_files=5, sparse_factor=8)
            if particle_data:
                analysis_results['particle'] = particle_data
                print("✓ 粒子数据分析完成")
        except Exception as e:
            print(f"✗ 粒子数据分析失败: {e}")
    else:
        print("跳过粒子数据分析")

    # 5. 总结分析结果
    print("\n" + "=" * 80)
    print("分析结果总结")
    print("=" * 80)

    for data_type, data in analysis_results.items():
        if data_type == 'cylinder':
            flow_shape = data['flow_data']['W'].shape
            print(f"✓ 圆柱绕流数据: 时间步={flow_shape[0]}, 空间点={flow_shape[1]*flow_shape[2]}")
        elif data_type == 'airfoil':
            dynamic_shape = data['dynamic_flow']['vorticity_reshaped'].shape
            print(f"✓ 翼型DNS数据: 时间步={dynamic_shape[0]}, 空间点={dynamic_shape[1]}")
        elif data_type == 'piv':
            velocity_shape = data['velocity']['u'].shape
            print(f"✓ PIV数据: 空间点={velocity_shape[0]}, 时间步={velocity_shape[1]}")
        elif data_type == 'particle':
            combined_shape = data['combined_features'].shape
            print(f"✓ 粒子数据: 时间步={combined_shape[0]}, 特征数={combined_shape[1]}")

    print(f"\n成功分析了 {len(analysis_results)} 种数据类型")
    print("所有数据都可用于后续的降维分析")

    # 6. 降维分析示例（如果有数据）
    if analysis_results:
        print("\n" + "=" * 80)
        print("降维分析示例")
        print("=" * 80)

        perform_dr = input("是否进行降维分析示例？(y/n): ").lower().strip()
        if perform_dr == 'y' or perform_dr == 'yes':

            # 选择第一个可用的数据集进行降维分析
            for data_type, data in analysis_results.items():
                print(f"\n对 {data_type} 数据进行降维分析...")

                try:
                    if data_type == 'cylinder':
                        # 使用涡量数据
                        X = get_data_for_dimensionality_reduction(data['flow_data'], field='W')
                    elif data_type == 'airfoil':
                        # 使用动态涡量数据
                        X = data['dynamic_flow']['vorticity_reshaped']
                    elif data_type == 'piv':
                        # 使用速度幅值数据
                        X = data['velocity']['magnitude'].T  # 转置为 (时间, 空间)
                    elif data_type == 'particle':
                        # 使用速度幅值数据
                        X = data['velocity_magnitude']
                    else:
                        continue

                    # 限制数据大小以提高计算效率
                    if X.shape[0] > 500:
                        X = X[:500]  # 限制时间步数
                    if X.shape[1] > 1000:
                        X = X[:, :1000]  # 限制空间点数

                    print(f"数据矩阵形状: {X.shape}")

                    # 进行降维方法比较
                    dr_results = compare_dimensionality_reduction_methods(X, n_components=8)

                    if dr_results:
                        analysis_results[f'{data_type}_dimensionality_reduction'] = dr_results
                        print(f"✓ {data_type} 数据降维分析完成")

                    # 只分析第一个数据集以节省时间
                    break

                except Exception as e:
                    print(f"✗ {data_type} 数据降维分析失败: {e}")
                    continue

    return analysis_results

# =============================================================================
# 使用说明和运行示例
# =============================================================================

if __name__ == "__main__":
    """
    主程序入口

    运行此脚本时会执行模块化的数据分析流程
    """
    print("降维方法基础与评估框架")
    print("=" * 60)
    print("本脚本包含以下功能：")
    print("1. 模块化的数据加载和处理函数")
    print("2. 多种数据集支持（圆柱绕流、翼型DNS、PIV、粒子数据）")
    print("3. 流场可视化函数")
    print("4. 力系数分析函数")
    print("5. 降维数据准备函数")
    print("6. 原始代码（保留用于参考）")
    print("=" * 60)

    print("\n可用的分析选项：")
    print("1. 圆柱绕流数据分析")
    print("2. 翼型DNS数据分析")
    print("3. PIV数据分析")
    print("4. 粒子数据分析")
    print("5. 综合数据分析（所有数据类型）")
    print("6. 跳过示例，直接使用函数")

    choice = input("\n请选择要运行的分析类型 (1-6): ").strip()

    if choice == '1':
        print("\n运行圆柱绕流数据分析...")
        analysis_results = main_analysis_example()
        if analysis_results:
            X_vorticity = get_data_for_dimensionality_reduction(analysis_results['flow_data'], field='W')
            print("圆柱绕流数据分析完成，可用于降维分析")

    elif choice == '2':
        print("\n运行翼型DNS数据分析...")
        try:
            airfoil_config = load_airfoil_dns_data()
            if airfoil_config:
                airfoil_flow = load_airfoil_flow_data(airfoil_config)
                if airfoil_flow:
                    visualize_airfoil_flow(airfoil_flow, data_type='dynamic', field='vorticity')
                    print("翼型DNS数据分析完成")
        except Exception as e:
            print(f"翼型DNS数据分析失败: {e}")

    elif choice == '3':
        print("\n运行PIV数据分析...")
        try:
            piv_data = load_cylinder_piv_data(sampling_step=200)
            if piv_data:
                visualize_piv_data(piv_data, field='magnitude')
                print("PIV数据分析完成")
        except Exception as e:
            print(f"PIV数据分析失败: {e}")

    elif choice == '4':
        print("\n运行粒子数据分析...")
        try:
            particle_data = load_particle_data(max_files=3, sparse_factor=8)
            if particle_data:
                print("粒子数据分析完成")
        except Exception as e:
            print(f"粒子数据分析失败: {e}")

    elif choice == '5':
        print("\n运行综合数据分析...")
        comprehensive_results = comprehensive_data_analysis_example()
        if comprehensive_results:
            print("综合数据分析完成")

    elif choice == '6':
        print("\n跳过示例运行。")
        print("您可以直接调用模块化函数进行数据分析。")
        print("\n主要函数包括：")
        print("圆柱绕流数据：")
        print("- load_cylinder_flow_data_A(): 加载流场数据")
        print("- process_cylinder_flow_data_A(): 处理流场数据")
        print("- visualize_cylinder_flow_snapshot(): 可视化流场")
        print("- load_cylinder_force_data_A(): 加载力系数数据")
        print("- visualize_force_coefficients(): 可视化力系数")
        print("\n翼型DNS数据：")
        print("- load_airfoil_dns_data(): 加载翼型配置")
        print("- load_airfoil_flow_data(): 加载翼型流场")
        print("- visualize_airfoil_flow(): 可视化翼型流场")
        print("\nPIV数据：")
        print("- load_cylinder_piv_data(): 加载PIV数据")
        print("- visualize_piv_data(): 可视化PIV数据")
        print("\n粒子数据：")
        print("- load_particle_data(): 加载粒子数据")
        print("\n数据准备：")
        print("- get_data_for_dimensionality_reduction(): 准备降维数据")

    else:
        print("无效选择，退出程序。")

    print("\n" + "=" * 60)
    print("程序执行完成")
    print("=" * 60)
