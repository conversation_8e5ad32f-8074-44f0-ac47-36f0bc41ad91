import os
import h5py
import numpy as np
import pandas as pd
import xarray as xr
import seaborn as sns
import matplotlib.pyplot as plt
import scipy.stats as ss
from scipy.stats import pearsonr
from scipy import stats
from PIL import Image
from tqdm import tqdm
import time
from scipy import fftpack
import scipy.integrate
from sklearn.metrics import mean_squared_error
from scipy.io import loadmat
from warnings import filterwarnings
from scipy.interpolate import interp2d
from sklearn import decomposition
from sklearn.decomposition import PCA,FastICA,FactorAnalysis,IncrementalPCA,KernelPCA,SparsePCA
from sklearn.manifold import MDS
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis as LDA#可以分类和降维
from sklearn.discriminant_analysis import QuadraticDiscriminantAnalysis as QDA#只能分类
from sklearn.manifold import TSNE
from sklearn.manifold import LocallyLinearEmbedding 
from sklearn.manifold._locally_linear import barycenter_kneighbors_graph
from sklearn.manifold import Isomap,SpectralEmbedding
from sklearn.metrics import euclidean_distances
from sklearn.metrics.pairwise import rbf_kernel
from sklearn.neighbors import kneighbors_graph
from scipy import signal
def smooth(a, nx, ny):
    kernel = np.ones((nx,ny)) * 1 / (nx * ny)
    r = signal.convolve2d(a, kernel, mode = 'same')
    return r
import cmasher as cmr
cmapv = cmr.get_sub_cmap('cmr.fusion_r', 0, 1, N=32)#
cmapp = cmr.get_sub_cmap('cmr.copper', 0, 1,)#N=32
cmapw = cmr.get_sub_cmap('cmr.viola', 0, 1,)#N=32
cmapwater = cmr.get_sub_cmap('cmr.ocean', 0, 1,)#N=32
cmap = cmr.get_sub_cmap('cmr.seasons', 0, 1,)#N=32
filterwarnings('ignore')#忽略警告

# ============================================================================
# 1. 数据生成和准备
# ============================================================================
print("\n1. 数据生成和准备")
print("-" * 40)
# ============================================================================
# 数据集选择和加载
# ============================================================================

# 数据集选择标志
USE_LORENZ = False  # 使用Lorenz系统
USE_AIRFOIL_DNS = True  # 使用低雷诺数俯仰翼型数据
USE_AIRFOIL_LES = False  # 使用NACA0012湍流数据
USE_CYLINDER_PIV = False  # 使用圆柱绕流PIV数据

# 初始化数据变量
X_multi = None
dataset_name = ""

if USE_LORENZ:
    # 使用Lorenz系统数据
    dataset_name = "Lorenz System"
    # 生成Lorenz系统数据
    def lorenz_system(state, t):
        sigma, rho, beta = 10, 28, 8/3
        x, y, z = state
        return [sigma * (y - x), x * (rho - z) - y, x * y - beta * z]
    
    t_span = (0, 30)
    dt = 0.01
    t = np.arange(t_span[0], t_span[1], dt)
    initial_state = [1.0, 1.0, 1.0]
    X_multi = odeint(lorenz_system, initial_state, t)
    
    print(f"生成Lorenz数据维度: {X_multi.shape}")
    print(f"变量说明: X_multi[:, 0]=x, X_multi[:, 1]=y, X_multi[:, 2]=z")
    print(f"使用数据集: {dataset_name}")

elif USE_AIRFOIL_DNS:
    # 低雷诺数的俯仰翼型数据
    dataset_name = "Airfoil DNS"
    print(f"使用数据集: {dataset_name}")

    try:
        plt.close('all')
        DIR = r'D:\基准数据\低雷诺数的俯仰翼'

        # 检查目录是否存在
        if not os.path.exists(DIR):
            raise FileNotFoundError(f"数据目录不存在: {DIR}")

        print(f"数据目录存在: {DIR}")

        # 相关参数加载
        params_file_path = os.path.join(DIR, 'airfoilDNS_parameters.h5')
        if not os.path.exists(params_file_path):
            raise FileNotFoundError(f"参数文件不存在: {params_file_path}")

        with h5py.File(params_file_path, 'r') as paramsFile:
            dt_field = paramsFile['/dt_field'][()]
            dt_force = paramsFile['/dt_force'][()]
            Re = paramsFile['/Re'][()]
            FreqsAll = paramsFile['/frequencies'][()]
            alpha_p = paramsFile['/alpha_p'][()]
            alpha_0s = paramsFile['/alpha_0s'][()]
            pitch_axis = paramsFile['/pitch_axis'][()]

        print("俯仰翼型参数加载成功")

        # 网格文件加载
        BaseAngle = 30

        filenameGrid = os.path.join(DIR, 'airfoilDNS_grid.h5')
        if not os.path.exists(filenameGrid):
            raise FileNotFoundError(f"网格文件不存在: {filenameGrid}")

        with h5py.File(filenameGrid, 'r') as gridFile:
            x = gridFile['/x'][()]
            y = gridFile['/y'][()]
            nx = len(x)
            ny = len(y)

        print("网格文件加载成功")

        # 数重采样函数
        def sample_every_ten(numbers):
            return numbers[::10]

        # 数据稀疏化
        sparse_value = 5
        nx_sparse = int(len(x[69:569])/sparse_value)
        ny_sparse = int(len(y[75:275])/sparse_value)

        plt_x = x[69:569][::sparse_value]
        plt_y = y[75:275][::sparse_value]

        # 数据选择
        FreqStrs = ['0p0','0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']
        FreqStr = '0p25'  # 选择准周期情况

        filename = os.path.join(DIR, f'airfoilDNS_a{BaseAngle}f{FreqStr}.h5')
        if not os.path.exists(filename):
            raise FileNotFoundError(f"数据文件不存在: {filename}")

        print(f"开始加载流场数据: {filename}")

        # 加载力系数数据
        with h5py.File(filename, 'r') as dataFile:
            Cl = dataFile['/Cl'][()]
            Cd = dataFile['/Cd'][()]
            alpha = dataFile['/alpha'][()]
            alphadot = dataFile['/alphadot'][()]
            cl = np.array(sample_every_ten(Cl))
            cd = np.array(sample_every_ten(Cd))
            alpha_sampled = np.array(sample_every_ten(alpha))
            alphadot_sampled = np.array(sample_every_ten(alphadot))

        print("力系数数据加载成功")

        # 加载流场数据
        nt = 1001
        with h5py.File(filename, 'r') as dataFile:
            xa = dataFile['/xa'][()]
            ya = dataFile['/ya'][()]
            ux = dataFile['/ux'][()]
            uy = dataFile['/uy'][()]
            vort = dataFile['/vort'][()]

            # 数据裁剪和稀疏化
            ux_cropped = ux[:, 75:275, 69:569][:,::sparse_value, ::sparse_value]
            uy_cropped = uy[:, 75:275, 69:569][:,::sparse_value, ::sparse_value]
            vort_cropped = vort[:, 75:275, 69:569][:,::sparse_value, ::sparse_value]

            # 计算速度幅值
            u = np.sqrt(ux_cropped * ux_cropped + uy_cropped * uy_cropped)

            # 重塑数据
            ureshape = u.reshape(nt, nx_sparse * ny_sparse)
            uxreshape = ux_cropped.reshape(nt, nx_sparse * ny_sparse)
            uyreshape = uy_cropped.reshape(nt, nx_sparse * ny_sparse)
            vortreshape = vort_cropped.reshape(nt, nx_sparse * ny_sparse)

        print("流场数据加载和处理成功")

        # 设置X_multi为俯仰翼型数据
        X_multis =np.array( [uxreshape,uyreshape,vortreshape])
        print(f"俯仰翼型数据加载成功，数据维度: {X_multis.shape}")

    except Exception as e:
        print(f"加载俯仰翼型数据失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        USE_AIRFOIL_DNS = False
        USE_LORENZ = True
        X_multi = None
        print("回退到Lorenz系统")

elif USE_AIRFOIL_LES:
    # NACA0012湍流数据集的可压缩流动
    dataset_name = "NACA0012 LES"
    print(f"使用数据集: {dataset_name}")

    try:
        plt.close('all')
        DIR = r'D:\基准数据\尾流的大涡模拟'

        # 检查目录是否存在
        if not os.path.exists(DIR):
            raise FileNotFoundError(f"数据目录不存在: {DIR}")

        print(f"数据目录存在: {DIR}")

        # 读取参数
        filename_parameters = os.path.join(DIR, 'airfoilLES_parameters.h5')
        if not os.path.exists(filename_parameters):
            raise FileNotFoundError(f"参数文件不存在: {filename_parameters}")

        with h5py.File(filename_parameters, 'r') as f:
            Re = f['/Re'][()]  # 雷诺数
            dt_les = f['/dt'][()]  # 快照之间的时间步长（传导时间单位）

        print(f"参数加载成功: Re={Re}, dt={dt_les}")

        # 读取网格信息
        filename_grid = os.path.join(DIR, 'airfoilLES_grid.h5')
        if not os.path.exists(filename_grid):
            raise FileNotFoundError(f"网格文件不存在: {filename_grid}")

        with h5py.File(filename_grid, 'r') as f:
            x1 = f['/x'][()]  # 流场网格的x坐标
            y1 = f['/y'][()]  # 流场网格的y坐标
            xa = f['/xa'][()]  # 翼型网格的x坐标
            ya = f['/ya'][()]  # 翼型网格的y坐标
            w = f['/w'][()]  # 流场网格的单元体积

        print(f"网格信息加载成功: 网格点数={len(x1)}")

        # 读取时间平均的中跨度流场信息
        filename_mean_midspan = os.path.join(DIR, 'airfoilLES_mean_midspan.h5')
        if not os.path.exists(filename_mean_midspan):
            raise FileNotFoundError(f"平均流场文件不存在: {filename_mean_midspan}")

        with h5py.File(filename_mean_midspan, 'r') as f:
            ux_mean = f['/ux_mean'][()]  # x方向速度的平均值
            uy_mean = f['/uy_mean'][()]  # y方向速度的平均值
            uz_mean = f['/uz_mean'][()]  # z方向速度的平均值

        print("平均流场数据加载成功")

        # 可选：绘制中跨度流场的平均流动（注释掉以加快加载速度）
        # plt.figure()
        # plt.scatter(x1, y1, 10, ux_mean)
        # plt.fill(xa, ya, [0.6, 0.6, 0.6])
        # plt.axis('equal')
        # plt.box(True)
        # plt.axis([-0.2, 2, -1, 1])
        # plt.clim([-0.2, 1.2])
        # plt.colorbar()
        # plt.title('NACA0012 LES - 平均流场')

        # 读取时间和z方向平均的流场信息
        filename_mean_zavg = os.path.join(DIR, 'airfoilLES_mean_zavg.h5')
        if not os.path.exists(filename_mean_zavg):
            raise FileNotFoundError(f"时间平均流场文件不存在: {filename_mean_zavg}")

        with h5py.File(filename_mean_zavg, 'r') as f:
            ux_zavg_mean = f['/ux_zavg_mean'][()]  # x方向速度的时间和z方向平均值

        # 读取流场快照
        jt_start = 5000
        jt_end = 6500
        jt_step = 10  # 每10个时间步取一个，减少内存使用
        jt_list = range(jt_start, jt_end, jt_step)
        ux_nacas = []

        # 检查快照目录
        snapshot_dir = os.path.join(DIR, 'airfoilLES_midspan')
        if not os.path.exists(snapshot_dir):
            raise FileNotFoundError(f"快照目录不存在: {snapshot_dir}")

        print(f"开始读取快照数据，时间步范围: {jt_start}-{jt_end}，步长: {jt_step}")

        # 读取中跨度的快照
        loaded_count = 0
        for i, jt in enumerate(jt_list):
            filename = os.path.join(snapshot_dir, f'airfoilLES_t{jt:05d}.h5')
            if os.path.exists(filename):
                try:
                    with h5py.File(filename, 'r') as f:
                        ux_naca = f['/ux'][()]  # x方向速度
                        ux_nacas.append(ux_naca)
                        loaded_count += 1
                except Exception as file_error:
                    print(f"警告: 无法读取文件 {filename}: {file_error}")
                    continue
            else:
                print(f"警告: 快照文件不存在: {filename}")

            # 进度显示
            if i % 10 == 0:
                print(f"已处理 {i}/{len(jt_list)} 个文件，成功加载 {loaded_count} 个")

        if len(ux_nacas) == 0:
            raise ValueError("没有找到任何有效的快照文件")

        if len(ux_nacas) < 10:
            raise ValueError(f"快照文件数量太少: {len(ux_nacas)}，至少需要10个")

        ux_nacas = np.array(ux_nacas).T  # N*T
        print(f"快照数据加载完成: {ux_nacas.shape}")

        # 可选：绘制中跨度切面的快照（注释掉以加快加载速度）
        # plt.figure()
        # plt.tripcolor(x1, y1, ux_nacas[:, 1], shading='gouraud', cmap=cmap)
        # plt.fill(xa, ya, color="white")
        # plt.plot(xa, ya, color='black', linewidth=2.0)
        # plt.axis('equal')
        # plt.axis([-0.2, 3, -0.5, 0.5])
        # plt.clim([-1.5, 1.5])
        # plt.colorbar()
        # plt.title('NACA0012 LES - 瞬时流场快照')
        # plt.show()

        # 设置X_multi为NACA0012数据
        X_multi = ux_nacas.T
        print(f"NACA0012湍流数据加载成功，数据维度: {X_multi.shape}")
      
    except Exception as e:
        print(f"加载NACA0012湍流数据失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        USE_AIRFOIL_LES = False
        USE_LORENZ = True
        X_multi = None
        print("回退到Lorenz系统")

elif USE_CYLINDER_PIV:
    # 圆柱绕流PIV数据
    dataset_name = "Cylinder PIV"
    print(f"使用数据集: {dataset_name}")

    try:
        # 瞬态条件下流过直径 d = 5 mm、长度 L = 20cm 的圆柱体的流动
        FOLDER = r'D:\基准数据\数据库\Ex_5_TR_PIV_Cylinder'

        # 检查目录是否存在
        if not os.path.exists(FOLDER):
            raise FileNotFoundError(f"数据目录不存在: {FOLDER}")

        print(f"数据目录存在: {FOLDER}")

        n_t = 13200  # 步数
        Fs = 3000
        dt_piv = 1 / Fs  # 数据采样频率为3kHz
        t_piv = np.arange(0, n_t) * dt_piv  # 准备时间轴
        # 71 × 30 点的网格,空间分辨率约为 Δx = 0.85mm

        # 读取网格文件
        mesh_file = os.path.join(FOLDER, 'MESH.dat')
        if not os.path.exists(mesh_file):
            raise FileNotFoundError(f"网格文件不存在: {mesh_file}")

        try:
            nxny = np.genfromtxt(mesh_file)  # 导入数据
            nxny = nxny[1:, :]  # 仅取数值数据部分
        except Exception as mesh_error:
            raise ValueError(f"网格文件读取失败: {mesh_error}")

        # 这是一个矢量数据的示例，因此空间点的数量应该加倍
        cy_x = nxny[:, 0]
        cy_y = nxny[:, 1]

        print(f"网格加载成功: {len(cy_x)} 个空间点")

        # 读取流场快照
        cy_data = []
        print("正在读取圆柱绕流PIV数据...")

        # 限制读取数量以避免内存问题
        max_files = min(3500, n_t)
        step_size = 5  # 每5个文件取一个，减少内存使用

        loaded_count = 0
        for i in range(0, max_files, step_size):
            filename = f'Res{i:05d}.dat'
            file_path = os.path.join(FOLDER, filename)
            if os.path.isfile(file_path):
                try:
                    data = np.genfromtxt(file_path)
                    if data.shape[0] > 1:  # 确保数据不为空
                        cy_data.append(data)
                        loaded_count += 1
                except Exception as file_error:
                    print(f"警告: 无法读取文件 {filename}: {file_error}")
                    continue

            if i % 500 == 0:
                print(f"已处理 {i}/{max_files} 个文件，成功加载 {loaded_count} 个")

        if len(cy_data) == 0:
            raise ValueError("没有找到任何有效的数据文件")

        if len(cy_data) < 50:
            raise ValueError(f"数据文件数量太少: {len(cy_data)}，至少需要50个")

        print(f"数据文件加载完成，共 {len(cy_data)} 个文件")

        try:
            cy_data = np.array(cy_data)
            cy_us = (cy_data[:, 1:, 0]).T[:, :len(cy_data)]
            print(f"数据处理完成: {cy_us.shape}")
        except Exception as process_error:
            raise ValueError(f"数据处理失败: {process_error}")

        # 可选：绘制中跨度切面的快照（注释掉以加快加载速度）
        # fig1, ax1 = plt.subplots(figsize=(16, 9))
        # circle = plt.Circle((-0.5, 0), 5, color='black')
        # snapshot_idx = min(250, cy_us.shape[1] - 1)
        # cnt1 = ax1.tripcolor(cy_x, cy_y, cy_us[:, snapshot_idx],
        #                     vmin=-2.5, vmax=15, cmap=cmap, shading='gouraud')
        # ax1.add_patch(circle)
        # plt.axis('equal')
        # fig1.colorbar(cnt1)
        # plt.title('圆柱绕流PIV - 瞬时流场')
        # plt.savefig('cylinder_piv_snapshot.png', dpi=300)
        # plt.show()

        # 设置X_multi为圆柱绕流数据
        max_time_steps = min(2000, cy_us.shape[1])  # 限制时间步数以节省内存
        X_multi = cy_us.T[:max_time_steps, :]
        print(f"圆柱绕流PIV数据加载成功，数据维度: {X_multi.shape}")
        print(f"数据统计: 最小值={X_multi.min():.4f}, 最大值={X_multi.max():.4f}, 均值={X_multi.mean():.4f}")

    except Exception as e:
        print(f"加载圆柱绕流PIV数据失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        USE_CYLINDER_PIV = False
        USE_LORENZ = True
        X_multi = None
        print("回退到Lorenz系统")


























#=========================================数据集选择和导入============================
"""
论文数据集
"""
#=========================================Re=100时圆柱绕流A============================
folder_path = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/circular_cylinder_0112'#稳态
folder_path = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/cylinder_data'#非稳态
file_list = [file for file in os.listdir(folder_path) if file.startswith("plt_") and file.endswith(".dat")]
data_cylinder_snapshot_A = []
for file_name in file_list:
    file_path = os.path.join(folder_path, file_name)
    file_data = np.loadtxt(file_path, skiprows=2)
    data_cylinder_snapshot_A.append(file_data)
#第一行为x坐标，第二行为y坐标，第三行为密度，第四行为速度u，第五行为速度v，第六行为压力，第七行为涡量。   
data_cylinder_snapshot_A = np.array(data_cylinder_snapshot_A)

#网格布置
nx=int(((34.98-0.02)/0.02)+2)
ny=int(((29.98-0.02)/0.02)+1)
print(nx*ny)
#稀疏化策略
sparse=4
#数据定义
X_cylinder_A=data_cylinder_snapshot_A[1,:,0].reshape(ny,nx)[549:949,449:1349][::sparse, ::sparse]
Y_cylinder_A=data_cylinder_snapshot_A[1,:,1].reshape(ny,nx)[549:949,449:1349][::sparse, ::sparse]
U_cylinder_A=data_cylinder_snapshot_A[:,:,3].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse, ::sparse]
V_cylinder_A=data_cylinder_snapshot_A[:,:,4].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse, ::sparse]
P_cylinder_A=data_cylinder_snapshot_A[:,:,5].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse, ::sparse]
W_cylinder_A=data_cylinder_snapshot_A[:,:,6].reshape(-1,ny,nx)[:,549:949,449:1349][:,::sparse, ::sparse]

#流场快照可视化
for i in range (0,1):
    plt.figure(figsize=(40, 16),dpi=300)
    z_cylinder_A = W_cylinder_A[i,:]
    clev = np.arange(-0.4, 0.45, 0.1)
    cf = plt.contourf(X_cylinder_A, Y_cylinder_A,z_cylinder_A,clev,cmap=cmap,extend='both')
    plt.xlim(X_cylinder_A.min(), X_cylinder_A.max())
    plt.ylim(Y_cylinder_A.min(), Y_cylinder_A.max())
    cbar = plt.colorbar(cf)
    cbar.set_label('Values')
    plt.xlabel('X Label')
    plt.ylabel('Y Label')
    plt.show()

#力的提取
sparse=6#6
file_path = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/circular_cylinder_0112/drag.dat'
#第一行为计算步数，换算成时间为“步数*0.02*0.1“，第二行为阻力系数，第三行为升力系数。
data_cylinder_force_A = np.loadtxt(file_path, skiprows=1)
time_cylinder_A=data_cylinder_force_A[5049:,0][::sparse]*0.02*0.1
cl_cylinder_A=data_cylinder_force_A[5049:,1][::sparse]
cd_cylinder_A=data_cylinder_force_A[5049:,2][::sparse]
#力系数可视化
plt.plot(time_cylinder_A,cl_cylinder_A)
plt.plot(time_cylinder_A,cd_cylinder_A)

sparse=50#50
file_path = 'E:/论文撰写/2024.01-02-物理学报(未投递)/数据/cylinder_data/drag.dat'
#第一行为计算步数，换算成时间为“步数*0.02*0.1“，第二行为阻力系数，第三行为升力系数。
data_cylinder_force_A = np.loadtxt(file_path, skiprows=1)
time_cylinder_A=data_cylinder_force_A[49:,0][::sparse]*0.02*0.1
cl_cylinder_A=data_cylinder_force_A[49:,1][::sparse]
cd_cylinder_A=data_cylinder_force_A[49:,2][::sparse]
#力系数可视化
plt.plot(time_cylinder_A,cl_cylinder_A)
plt.plot(time_cylinder_A,cd_cylinder_A)
# =========================================Re=100时圆柱绕流B===============================
# data_cylinder_snapshot_B = loadmat("D:/基准数据/DATA/DATA/FLUIDS/CYLINDER_ALL.mat")
# #构建网格数据
# def generate_grid(x_range, y_range):
#     grid_points = []
#     for y in y_range:
#         for x in x_range:
#             grid_points.append((x, y))
#     return grid_points
# #指定x和y的范围
# x_range =  np.linspace(-1, 9, 449)# -1到9的范围
# y_range =  np.linspace(-2, 2, 199)# 0到2的范围
# #生成二维网格点的坐标分布
# grid_points = np.array(generate_grid(x_range, y_range))# 位置向量矩阵，r = (xi,yj)
# dt=0.2#时间差

# X_cylinder_B=grid_points[:,0].reshape(199,449)# 位置矩阵x，xi,xj
# Y_cylinder_B=grid_points[:,1].reshape(199,449)# 位置矩阵y，yi,yj

# U_cylinder_B = data_cylinder_snapshot_B["UALL"]  # N x T：速度X
# U_cylinder_B_mean=np.mean(U_cylinder_B, axis=1)

# tstep=10#步骤
# U_cylinder_B_plu =U_cylinder_B[:,tstep]-U_cylinder_B_mean
# U_cylinder_B_plu_mean=np.mean(abs(U_cylinder_B_plu.reshape(449,199).T), axis=0)
# #单点曲线
# U_cylinder_B_snapshot1=U_cylinder_B.reshape(449,199,151)[157,134,:]
# plt.plot(U_cylinder_B_snapshot1)

# V_cylinder_B = data_cylinder_snapshot_B["VALL"]  # N x T: 速度Y
# V_cylinder_B_mean=np.mean(V_cylinder_B, axis=1)
# V_cylinder_B_plu =V_cylinder_B[:,tstep]-V_cylinder_B_mean
# V_cylinder_B_plu_mean=np.mean(abs(V_cylinder_B_plu.reshape(449,199).T), axis=0)

# W_cylinder_B = data_cylinder_snapshot_B["VORTALL"]  #N x T: 涡量

# #可视化
# z=U_cylinder_B[:,tstep].reshape(449,199).T
# z=U_cylinder_B_mean.reshape(449,199).T
# z=U_cylinder_B_plu.reshape(449,199).T
# fig1, ax1 = plt.subplots(figsize = (16, 9))
# circle = plt.Circle((0.09, 0), 0.52, color = 'black')
# clev = np.arange(-0.4, 0.45, 0.1)
# cnt1 = ax1.contourf(X_cylinder_B, Y_cylinder_B, z, clev, cmap = plt.cm.coolwarm, extend='both')
# ax1.add_patch(circle)
# plt.axis('equal')
# fig1.colorbar(cnt1)

# =========================================Re=100时2D圆柱绕流C===============================
data_cylinder_snapshot_C = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D.mat")#10边型的局部观测
#data_cylinder_snapshot_C = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_flower.mat")#10边型的局部观测
#时间差0.08s
sparse=1
C_star = data_cylinder_snapshot_C["C_star"][::sparse,:]  # N x 2 x T：浓度C
U_star = data_cylinder_snapshot_C["U_star"][::sparse,:]  # N x 2 x T：速度X
V_star = data_cylinder_snapshot_C["V_star"][::sparse,:]  # N x 2 x T：速度Y
P_star = data_cylinder_snapshot_C["P_star"][::sparse,:]   # N x T:压力
T_star = data_cylinder_snapshot_C["t_star"][::sparse,:]   # T x 1:时间
X_star = data_cylinder_snapshot_C["x_star"][::sparse,:]  # N x 2：位置：X,Y
Y_star = data_cylinder_snapshot_C["y_star"][::sparse,:]  # N x 2：位置：X,Y

#绘制中跨度切面的快照
plt.figure()
fig1, ax1 = plt.subplots(figsize=(16, 9),dpi=300)
plt.tripcolor(X_star[:,0], Y_star[:,0],U_star[:,100], shading='gouraud', cmap=cmapv)
plt.axis('equal')
plt.colorbar()
plt.show()
# =========================================Re=100时2D圆柱绕流C(时间差影响)===============================
# data_cylinder_snapshot_C1 = loadmat("D:/基准数据/圆柱绕流数据集/HPM-master/Data/cylinder.mat")##时间差0.2s
# data_cylinder_snapshot_C2 = loadmat("D:/基准数据/圆柱绕流数据集/HPM-master/Data/cylinder_fine.mat")#时间差0.02s
# data_cylinder_snapshot_C3 = loadmat("D:/基准数据/圆柱绕流数据集/HPM-master/Data/cylinder_finer.mat")#时间差0.01s
# data_cylinder_snapshot_C4 = loadmat("D:/基准数据/圆柱绕流数据集/HPM-master/Data/cylinder_finest.mat")#时间差0.005s

# U_star = data_cylinder_snapshot_C1["U_star"][:,0,:]  # N x 2 x T：速度X
# V_star = data_cylinder_snapshot_C1["U_star"][:,1,:]  # N x 2 x T：速度Y
# T_star = data_cylinder_snapshot_C1["t_star"]  # T x 1:时间
# X_star = data_cylinder_snapshot_C1["X_star"][:,0] # N x 2：位置：X,Y
# Y_star = data_cylinder_snapshot_C1["X_star"][:,1] # N x 2：位置：X,Y

# #绘制中跨度切面的快照
# plt.figure()
# fig1, ax1 = plt.subplots(figsize=(16, 9),dpi=300)
# plt.tripcolor(X_star[:,], Y_star[:,],U_star[:,100], shading='gouraud', cmap=cmapv)
# plt.axis('equal')
# plt.colorbar()
# plt.show()

# =========================================Re=100时圆柱绕流D(边界影响)===============================
# data_cylinder_snapshot_D = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_Re200Pec2000_Dirichlet_Streaks.mat")
# data_cylinder_snapshot_D = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_Re200Pec2000_Neumann_Streaks.mat")
# data_cylinder_snapshot_D = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_Re200Pec200_Dirichlet_Streaks_Forces.mat")
# data_cylinder_snapshot_D = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder2D_Re200Pec200_Neumann_Streaks_Forces.mat")
# C_data  = data_cylinder_snapshot_D["C_data"]  # N x 2 x T：浓度C
# U_data  = data_cylinder_snapshot_D["U_data"]  # N x 2 x T：速度X
# V_data  = data_cylinder_snapshot_D["V_data"]  # N x 2 x T：速度Y
# P_data  = data_cylinder_snapshot_D["P_data"]  # N x T:压力
# T_data  = data_cylinder_snapshot_D["t_data"]  # T x 1:时间
# X_data  = data_cylinder_snapshot_D["x_data"]  # N x 2：位置：X,Y
# Y_data  = data_cylinder_snapshot_D["y_data"] # N x 2：位置：X,Y
# #绘制中跨度切面的快照
# plt.figure()
# fig1, ax1 = plt.subplots(figsize=(16, 9),dpi=300)
# plt.tripcolor(X_data[:,0], Y_data[:,0],U_data[:,100], shading='gouraud', cmap=cmapv)
# plt.axis('equal')
# plt.colorbar()
# plt.show()

# =========================================Re=100时圆柱绕流E(3D)===============================
data_cylinder_snapshot_E = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Cylinder3D.mat")#3D

C_star = data_cylinder_snapshot_E["C_star"]  # N x 2 x T：浓度C
U_star = data_cylinder_snapshot_E["U_star"]  # N x 2 x T：速度X
V_star = data_cylinder_snapshot_E["V_star"]  # N x 2 x T：速度Y
W_star = data_cylinder_snapshot_E["W_star"] # N x 2 x T：速度Z
P_star = data_cylinder_snapshot_E["P_star"]  # N x T:压力
T_star = data_cylinder_snapshot_E["t_star"]  # T x 1:时间
X_star = data_cylinder_snapshot_E["x_star"]  # N x 2：位置：X,Y
Y_star = data_cylinder_snapshot_E["y_star"] # N x 2：位置：X,Y
Z_star = data_cylinder_snapshot_E["z_star"] # N x 2：位置：X,Y
# =========================================其他绕流===============================
#data_cylinder_snapshot_F = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Davinci.mat")#10边型的局部观测
#data_cylinder_snapshot_F = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/real_aneurysm.mat")#10边型的局部观测
#data_cylinder_snapshot_F = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/real_aneurysm_shear.mat")#10边型的局部观测
#data_cylinder_snapshot_F = loadmat("D:/基准数据/圆柱绕流数据集/HFM-master/PINNData/Stenosis2D.mat")#10边型的局部观测
# C_star = data_cylinder_snapshot_F["C_star"]  # N x 2 x T：浓度C
# U_star = data_cylinder_snapshot_F["U_star"]  # N x 2 x T：速度X
# V_star = data_cylinder_snapshot_F["V_star"]  # N x 2 x T：速度Y
# P_star = data_cylinder_snapshot_F["P_star"]  # N x T:压力
# T_star = data_cylinder_snapshot_F["t_star"]  # T x 1:时间
# X_star = data_cylinder_snapshot_F["x_star"]  # N x 2：位置：X,Y
# Y_star = data_cylinder_snapshot_F["y_star"] # N x 2：位置：X,Y

# #绘制中跨度切面的快照
# plt.figure()
# fig1, ax1 = plt.subplots(figsize=(16, 9),dpi=300)
# plt.tripcolor(X_star[:,0], Y_star[:,0],C_star[:,0], shading='gouraud', cmap=cmapv)
# plt.axis('equal')
# plt.colorbar()
# plt.show()

# =========================================低雷诺数的俯仰翼型===============================
plt.close('all')
DIR = 'D:\基准数据\低雷诺数的俯仰翼'
# 相关参数
paramsFile = h5py.File(os.path.join(DIR, 'airfoilDNS_parameters.h5'), 'r')
dt_field = paramsFile['/dt_field'][()]
dt_force = paramsFile['/dt_force'][()] 
Re = paramsFile['/Re'][()]
FreqsAll = paramsFile['/frequencies'][()]
alpha_p = paramsFile['/alpha_p'][()] 
alpha_0s = paramsFile['/alpha_0s'][()]
pitch_axis = paramsFile['/pitch_axis'][()]
paramsFile.close()

BaseAngle = 30 
tstep = 5

filenameGrid = os.path.join(DIR, 'airfoilDNS_grid.h5')
with h5py.File(filenameGrid, 'r') as gridFile:
    x = gridFile['/x'][()]
    y = gridFile['/y'][()]
    nx = len(x)
    ny = len(y)
    
# 数重采样
def sample_every_ten(numbers):
    new_sequence = numbers[::10]
    return new_sequence

# 数据稀疏化
sparse_value=2

#nx = int(len(x[69:569])/sparse_value)
nx = int(len(x[69:569])/sparse_value)
ny = int(len(y[75:275])/sparse_value)

#plt_x=x[69:569][::sparse_value]
plt_x=x[69:569][::sparse_value]
plt_y=y[75:275][::sparse_value]
    
#数据选择
#FreqStr = '0p0'   # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']#非周期
#FreqStr = '0p05'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']#非周期
#FreqStr = '0p1'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']准周期
#FreqStr = '0p2'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期
#FreqStr = '0p25'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期
#FreqStr = '0p3'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期
#FreqStr = '0p35'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期
#FreqStr = '0p4'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']#准周期
#FreqStr = '0p5'  # ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']周期

FreqStrs=['0p0','0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']
FreqStr = '0p2'# ['0p05','0p1','0p2','0p25','0p3','0p35', '0p4','0p5']
Freq = 0# [0.05,0.1,0.2, 0.25,0.3,0.35,0.4,0.5]

filename = os.path.join(DIR, f'airfoilDNS_a30static.h5')
#filename = os.path.join(DIR, f'airfoilDNS_a25static.h5')
with h5py.File(filename, 'r') as dataFile:
    Cl = dataFile['/Cl'][()]
    Cd = dataFile['/Cd'][()]
    alpha = dataFile['/alpha'][()]
    alphadot = dataFile['/alphadot'][()]
    xa = dataFile['/xa'][()]
    ya = dataFile['/ya'][()]
    ux = dataFile['/ux'][()]
    uy = dataFile['/uy'][()]
    vort=dataFile['/vort'][()]
    ux = ux[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    uy = uy[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    vort = vort[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    u = np.sqrt(ux * ux + uy * uy)
    ureshape = u.reshape(401, nx * ny)
    uxreshape = (ux).reshape(401, nx * ny)
    uyreshape = (uy).reshape(401, nx * ny)
    vortreshape = (vort).reshape(401, nx * ny)
    cl= np.array(sample_every_ten(Cl.T))
    cd = np.array(sample_every_ten(Cd.T))
    alpha = np.array(sample_every_ten(alpha.T))
    alphadot = np.array(sample_every_ten(alphadot.T))   

filename = os.path.join(DIR, f'airfoilDNS_a{BaseAngle}f{FreqStr}.h5')
with h5py.File(filename, 'r') as dataFile:
    Cl = dataFile['/Cl'][()]
    Cd = dataFile['/Cd'][()]
    alpha = dataFile['/alpha'][()]
    alphadot = dataFile['/alphadot'][()]
    cl= np.array(sample_every_ten(Cl))
    cd = np.array(sample_every_ten(Cd))
    alpha = np.array(sample_every_ten(alpha))
    alphadot = np.array(sample_every_ten(alphadot))   
#nt = 401   
nt = 1001
filename = os.path.join(DIR, f'airfoilDNS_a{BaseAngle}f{FreqStr}.h5')
with h5py.File(filename, 'r') as dataFile:
    xa = dataFile['/xa'][()]
    ya = dataFile['/ya'][()]
    ux = dataFile['/ux'][()]
    uy = dataFile['/uy'][()]
    vort = dataFile['/vort'][()]
    ux = ux[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    uy = uy[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    vort = vort[:, 75:275, 69:569][:,::sparse_value, ::sparse_value,]
    u = np.sqrt(ux * ux + uy * uy)
    ureshape = u.reshape(nt, nx * ny)
    uxreshape = (ux).reshape(nt, nx * ny)
    uyreshape = (uy).reshape(nt, nx * ny)
    vortreshape = (vort).reshape(nt, nx * ny)
    
#=============================================================================
i=1
#可视化
plt.figure(figsize=(20, 6),dpi=200) 
z = ux[36, :, :]
z = uy[36, :, :]
z = vort[36, :, :]
cf = plt.contourf(plt_x, plt_y, z, 
                  levels=np.linspace(-3, 3, 32), vmin=-3, vmax=3,
                  cmap='RdBu_r',extend='both')
cf.set_clim(-3, 3)
cbar = plt.colorbar(cf)
cbar.set_label('Values')
plt.xlabel('X Label')
plt.ylabel('Y Label')
plt.show()
# #=============================================================================
# import imageio
# images = []
# for tstep in range(0, 1, 5):
#     plt.figure(figsize=(10, 4))  # 调整图像尺寸
#     z = vort[tstep, :]
#     cf = plt.contourf(x[:], y[74:276], z,
#                       levels=np.linspace(-3, 3, 18), vmin=-3, vmax=3,
#                       cmap='RdBu_r', extend='both')
#     cf.set_clim(-3, 3)
#     #plt.plot(xa[:, tstep], ya[:, tstep], 'k-')
#     plt.axis('off')  # 关闭坐标轴显示
#     # plt.xlim(x.min(), x.max())
#     # plt.ylim(y[74], y[276])
#     # cbar = plt.colorbar(cf)
#     # cbar.set_label('Values')
#     # plt.xlabel('X', fontsize=15)  # 调整轴标签字体大小
#     # plt.ylabel('Y', fontsize=15)  # 调整轴标签字体大小
#     # plt.title('Title', fontsize=20)  # 添加图像标题，调整字体大小
#     plt.tight_layout()  # 调整布局，防止标签被切断
#     # 保存每一帧图像
#     filename = f'frame_{tstep:03d}.png'
#     plt.savefig(filename, dpi=800)  # 调整保存图像的分辨率
#     # 将当前图像添加到图像列表中
#     images.append(imageio.imread(filename))
#     # 关闭当前图像，准备绘制下一帧
#     plt.close()
# # 使用imageio库将图像列表保存为GIF
# imageio.mimsave('animation.gif', images, duration=0.1)
# # 删除临时生成的单独图像文件
# for filename in set(f'frame_{tstep:03d}.png' for tstep in range(0, 1000, 5)):
#     os.remove(filename)

# #获取监测点数据
# ux1=ux[:,75,174]
# ux2=ux[:,100,174]
# ux3=ux[:,125,174]
# time=np.arange(50, 150.1, 0.1)#预测时间
# #可视化
# fig, (ax1, ax2, ax3) = plt.subplots(3, sharex=True,dpi=1200)
# ax1.plot(time, ux1, color='black',label=r"$A$")
# ax2.plot(time, ux2, color='black',label=r"$B$")
# ax3.plot(time, ux3, color='black',label=r"$C$")
# ax3.set_xlabel(r"$t$")
# plt.show()
# =========================================二、数据集的导入===============================
#圆柱绕流PIV
#瞬态条件下流过直径 d = 5 mm、长度 L = 20cm 的圆柱体的流动
# 1. 数据准备
#FOLDER = 'C:/Users/<USER>/Desktop/降维算法的评价/Ex_5_TR_PIV_Cylinder'
FOLDER =  'D:/基准数据/MODULO-master/Matlab_Exercises/Exercise_5/Data'
n_t = 13200  # 步数
Fs = 3000
dt = 1 / Fs  # 数据采样频率为3kHz
t = np.arange(0, n_t) * dt  # 准备时间轴
#71 × 30 点的网格,空间分辨率约为 Δx = 0.85mm

# 读取一个网格文件
file = os.path.join(FOLDER, 'MESH.dat')
nxny = np.genfromtxt(file)  # 导入数据
nxny = nxny[1:, :]  # 仅取数值数据部分

# 这是一个矢量数据的示例，因此空间点的数量应该加倍
cy_x = nxny[:, 0]
cy_y = nxny[:, 1]

#0-4000
#4000-7000
#7000-
# 读取流场快照
cy_data=[]
for i in range(0, 13200):
    filename = f'Res{i:05d}.dat'
    file_path = os.path.join(FOLDER, filename)
    if os.path.isfile(file_path):
        cy_data.append(np.genfromtxt(file_path))
    
cy_data=np.array(cy_data)    
cy_us=(cy_data[:,1:,0]).T
cy_vs=(cy_data[:,1:,1]).T

Amode1_mean=np.mean(cy_us[:, :4000], axis=1)
Amode2_mean=np.mean(cy_us[:, 4000:7000], axis=1)
Amode3_mean=np.mean(cy_us[:, 7000:], axis=1)

#1236,观察速度变化：x,y=30,2
#2500
#5000
#10000
# Au=cy_us[1236, ]
# Av=cy_vs[1236, ]
# snapshot_u=(cy_us[:, 10000]).reshape(30,71)
# snapshot_v=(cy_vs[:, 10000]).reshape(30,71)
# A1=snapshot_u-z1
# A2=snapshot_v-z2
# A1_mean=np.mean(A1, axis=0)
# A2_mean=np.mean(A2, axis=0)
# plt.tripcolor(A1, shading='gouraud',cmap=cmap)

# 绘制中跨度切面的快照 
fig1, ax1 = plt.subplots(figsize = (16, 9))
circle = plt.Circle((-0.5, 0), 5, color = 'black')
cnt1 = ax1.tripcolor(cy_x, cy_y, cy_us[:, 2500],vmin=-2.5,vmax=15,  cmap = cmap, shading='gouraud', )
ax1.add_patch(circle)
plt.axis('equal')
fig1.colorbar(cnt1)
plt.savefig('high_resolution_plot.png', dpi=300) 

# 绘制中跨度切面的快照
fig1, ax1 = plt.subplots(figsize = (16, 9))
circle = plt.Circle((-0.5, 0), 5, color = 'black')
cnt1 = ax1.tripcolor(cy_x, cy_y, Amode1_mean,vmin=-2.5,vmax=15,  cmap = cmap, shading='gouraud', )
ax1.add_patch(circle)
plt.axis('equal')
fig1.colorbar(cnt1)
plt.savefig('high_resolution_plot.png', dpi=300)
#=========================================NACA0012湍流数据集的可压缩流动===============================
plt.close('all')
DIR = 'D:\基准数据\尾流的大涡模拟'

# 读取参数
filename_parameters = DIR + '/airfoilLES_parameters.h5'
with h5py.File(filename_parameters, 'r') as f:
    Re = f['/Re'][()]  # 雷诺数
    dt = f['/dt'][()]  # 快照之间的时间步长（传导时间单位）

# 读取网格信息
filename_grid = DIR + '/airfoilLES_grid.h5'
with h5py.File(filename_grid, 'r') as f:
    x = f['/x'][()]  # 流场网格的x坐标 
    y = f['/y'][()]  # 流场网格的y坐标
    xa = f['/xa'][()]  # 翼型网格的x坐标
    ya = f['/ya'][()]  # 翼型网格的y坐标
    w = f['/w'][()]  # 流场网格的单元体积

# 读取时间平均的中跨度流场信息
filename_mean_midspan = DIR + '/airfoilLES_mean_midspan.h5'
with h5py.File(filename_mean_midspan, 'r') as f:
    ux_mean = f['/ux_mean'][()]  # x方向速度的平均值
    uy_mean = f['/uy_mean'][()]  # y方向速度的平均值
    uz_mean = f['/uz_mean'][()]  # z方向速度的平均值

# 绘制中跨度流场的平均流动
# dx=x[1]-x[2]
# nx=(max(x)-min(x))
# print(nx)
# dy=y[1]-y[2]
# ny=(max(y)-min(y))/dy
# print(ny)

plt.figure()
fig1, ax1 = plt.subplots(figsize=(16, 9))
plt.tripcolor(x, y, ux_mean, shading='gouraud', cmap=cmapv)
plt.fill(xa, ya, color="white")
plt.plot(xa, ya, color='black', linewidth=2.0)
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])
#plt.clim([-0.2, 1.0])
plt.colorbar()
plt.show()

# 读取时间和z方向平均的流场信息
# filename_mean_zavg = DIR + '/airfoilLES_mean_zavg.h5'
# with h5py.File(filename_mean_zavg, 'r') as f:
#     ux_zavg_mean = f['/ux_zavg_mean'][()]  # x方向速度的时间和z方向平均值
#     uy_zavg_mean = f['/uy_zavg_mean'][()]  # y方向速度的时间和z方向平均值
#     uz_zavg_mean = f['/uz_zavg_mean'][()]  # z方向速度的时间和z方向平均值

# # 绘制时间和z方向平均的流场
# plt.figure()
# plt.scatter(x, y, 10, ux_zavg_mean,)#数值坐标
# plt.fill(xa, ya, [0.6, 0.6, 0.6],color="white")#翼型坐标
# plt.axis('equal')
# plt.box(True)
# plt.axis([-0.2, 2, -1, 1])
# plt.clim([-0.2, 1.2])
# plt.colorbar()

# 读取流场快照
jt_list = range(5000, 10000)
jj = 0
ux_nacas=[]
uy_nacas=[]
uz_nacas=[]

# 读取中跨度的快照
for jt in jt_list:
    jj += 1
    filename = DIR + '/airfoilLES_midspan/airfoilLES_t{:05d}.h5'.format(jt)
    with h5py.File(filename, 'r') as f:
        ux_naca = f['/ux'][()]  # x方向速度
        ux_nacas.append(ux_naca)
        uy_naca = f['/uy'][()]  # y方向速度
        uy_nacas.append(uy_naca)
        uz_naca = f['/uz'][()]  # z方向速度
        uz_nacas.append(uz_naca)
        
ux_nacas=(np.array(ux_nacas)).T #N*T
uy_nacas=(np.array(uy_nacas)).T #N*T
uz_nacas=(np.array(uz_nacas)).T #N*T
 
tstep=10
# 绘制中跨度切面的快照
plt.figure()
fig1, ax1 = plt.subplots(figsize=(16, 9))
plt.tripcolor(x, y, uz_nacas[:,10], shading='gouraud', cmap=cmapv)
plt.fill(xa, ya, color="white")
plt.plot(xa, ya, color='black', linewidth=2.0)
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])
#plt.clim([-0.2, 1.0])
plt.colorbar()
plt.show()

plt.figure()
fig1, ax1 = plt.subplots(figsize=(16, 9))
plt.tripcolor(x, y, uz_nacas[:,10]-ux_mean, shading='gouraud', cmap=cmapv)
plt.fill(xa, ya, color="white")
plt.plot(xa, ya, color='black', linewidth=2.0)
plt.axis('equal')
plt.axis([-0.2, 3, -0.5, 0.5])
#plt.clim([-0.2, 1.0])
plt.colorbar()
plt.show()

# =========================================空化数据集的导入==============================
from Extract_Data_RANS2022 import Data_class#原始数据位置已调整#887
from Extract_Data_RANS2023 import Data_class#原始数据位置已调整#1866
from Extract_Data_DES import Data_class#原始数据位置已调整#1093
from Extract_Data_LES import Data_class#原始数据位置已调整#819
time_num  = 887#时间数887，LES819
row_num   = 125#行数
num_ppr   = 500#列数
Reduction = 0#列数减少

#2.数据类型
fieldV = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='速度')
fieldVX = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='X速度')
fieldVY = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Y速度')
fieldVZ = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Z速度')
fieldW = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='涡量')
fieldWX = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='X涡量')
fieldWY = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Y涡量')
fieldWZ = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='Z涡量')
fieldP = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='压力')
fieldD = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='密度')
fieldwater = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='水体积分数')
fieldvapor = Data_class(num_time=time_num, num_row=row_num, num_ppr=num_ppr, reduction=Reduction, fun='水蒸汽体积分数')

#3.数据定义
#3.1原始数据1：数据平铺*时间
V = fieldV.extract_data()[:,:time_num]
P = fieldP.extract_data()[:,:time_num]
W = fieldW.extract_data()[:,:time_num]
Water = fieldwater.extract_data()[:,:time_num]
#Vapor = fieldvapor.extract_data()[:,:time_num]
#D  = fieldD.extract_data()[:,:time_num]
#VX = fieldVX.extract_data()[:,:time_num]
#VY = fieldVY.extract_data()[:,:time_num]
#VZ = fieldVZ.extract_data()[:,:time_num]
#WX = fieldWX.extract_data()[:,:time_num]
#WY = fieldWY.extract_data()[:,:time_num]
#WZ = fieldWZ.extract_data()[:,:time_num]

#3.2原始数据2：数据：X*Y*时间
#3.3原始数据3：数据：X*Y*时间(数据选择)RANS:0-221,一个周期,121-296 296-475
#52-270 270-431
time_num1=188#105
time_num2=823#431
num=time_num2-time_num1#选取的数据个数

# 数据稀疏化
sparse_value=2
V2=np.array(V).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
P2=np.array(P).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
W2=np.array(W).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
Water2=np.array(Water).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# Vapor2=np.array(Vapor).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# D2  = np.array(D).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# VX2  = np.array(VX).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# VY2  = np.array(VY).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# VZ2  = np.array(VZ).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# WX2  = np.array(WX).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# WY2  = np.array(WY).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
# WZ2  = np.array(WZ).reshape(row_num,-1,time_num)[:,:,time_num1:time_num2][::sparse_value,::sparse_value,]
#sns.heatmap(round(pd.DataFrame(V2[:,:,2]),2),cmap='RdBu_r')

#3.0原始数据0：数据：空间*时间
V1 = V2.reshape(-1,num)
P1 = P2.reshape(-1,num)
W1 = W2.reshape(-1,num)
Water1 = Water2.reshape(-1,num)
# Vapor1 = Vapor2.reshape(-1,num)
# D1 = D2.reshape(-1,num)
# VX1 = VX2.reshape(-1,num)
# VY1 = VY2.reshape(-1,num)
# VZ1 = VZ2.reshape(-1,num)
# WX1 = WX2.reshape(-1,num)
# WY1 = WY2.reshape(-1,num)
# WZ1 = WZ2.reshape(-1,num)

#3.6空间维度和变量维度均缩减平铺：空间变量均降维，变量标准化。
from sklearn.preprocessing import StandardScaler,MinMaxScaler#标准化函数
transfer1=StandardScaler()#标准化函数Z-score标准化方法
transfer2=MinMaxScaler()#标准化函数Z-score标准化方法
data_new=[]
for j in range(num):
    V2=V1[:,j].reshape(-1,1)
    P2=P1[:,j].reshape(-1,1)
    W2=W1[:,j].reshape(-1,1)
    Water2=Water1[:,j].reshape(-1,1)
    #Vapor2=Vapor1[:,j].reshape(-1,1)
    #D2=D1[:,j].reshape(-1,1)
    #VX2=VX1[:,j].reshape(-1,1)
    #VY2=VY1[:,j].reshape(-1,1)
    #VZ2=VZ1[:,j].reshape(-1,1)
    #WX2=WX1[:,j].reshape(-1,1)
    #WY2=WY1[:,j].reshape(-1,1)
    #WZ2=WZ1[:,j].reshape(-1,1)
    data_new1 = np.concatenate((V2,P2,W2,Water2,),axis=1)
    data_new1 = transfer1.fit_transform(data_new1)        
    data_new.append(data_new1.tolist())
data_new=np.array(data_new)
# =========================================================================================================
#4.数据可视化
#数据平滑
from scipy import signal
def smooth(a, nx, ny):
    kernel = np.ones((nx,ny)) * 1 / (nx * ny)
    r = signal.convolve2d(a, kernel, mode = 'same')
    return r

#颜色条设置
import cmasher as cmr
cmapv = cmr.get_sub_cmap('cmr.fusion_r', 0, 1,N=64 )#N=32
cmapp = cmr.get_sub_cmap('cmr.copper', 0, 1,N=64)#N=32
cmapw = cmr.get_sub_cmap('cmr.viola', 0, 1,)#N=32
cmapwater = cmr.get_sub_cmap('cmr.ocean', 0, 1,)#N=32
cmap = cmr.get_sub_cmap('cmr.seasons', 0, 1,N=64)#
#'RdBu_r'(红蓝白),'RdBu'（红蓝反向）,'rainbow'(红蓝)
"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
论文内容
"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
#######################################相空间重构，不完全可观测，时延嵌入#################
"""
论文内容1
"""
##########################################降维方法的应用与评价##########################
"""
论文内容2
"""
from PCAWZH2024 import PCAWZH
from SVDWZH2024 import SVDWZH
from ICAWZH2024 import ICAWZH
from NMFWZH2024 import NMFWZH
from KPCAWZH2024 import KPCAWZH
from ISOMAPWZH2024 import ISOMAPWZH
from LLEWZH2024 import LLEWZH
from LEMWZH2024 import LEMWZH
from sklearn.metrics import mutual_info_score
from sklearn.preprocessing import MinMaxScaler

#X=uxreshape#时间
X=U_star.T#时间
#X=X.T#空间
"""
线性降维PCA
"""
# from sklearn.preprocessing import StandardScaler#标准化函数
# transfer=StandardScaler()#标准化函数Z-score标准化方法
# X = transfer.fit_transform(X) 
#K的选择
rand_scores_pcas=[]
nmi_scores_pcas=[]
fmi_scores_pcas=[]
silhouette_scores_pcas=[]
for k in range(5,75,5):
    print(k)
    pca = PCAWZH(r_max=10, r_step=1,n_cluster=k)
    mode_pca,scaler_mode_pca= pca.fit_transform(X)
    reconstructed_pca,reconstructed_pcas,err_pca,err_rmse_pca,low_dims_pca,high_dims_pca,all_structure_corrs_pca,all_structure_mi_scores_pca,all_structure_dtws_pca,all_structure_trustworthinesss_pca,rand_scores_pca,nmi_scores_pca,fmi_scores_pca,silhouette_scores_pca,class_scores_pca,transition_matrixs_pca,err_tpms_pca,err_tpm_values_pca,r2s_pca,mses_pca,sampens_pca,hursts_pca,lyaps_pca= pca.inverse_transform(X)
    rand_scores_pcas.append(rand_scores_pca)
    nmi_scores_pcas.append(nmi_scores_pca)
    fmi_scores_pcas.append(fmi_scores_pca)
    silhouette_scores_pcas.append(silhouette_scores_pca)
rand_scores_pcas=np.array(rand_scores_pcas)
nmi_scores_pcas=np.array(nmi_scores_pcas)
fmi_scores_pcas=np.array(fmi_scores_pcas)
silhouette_scores_pcas=np.array(silhouette_scores_pcas)

pca = PCAWZH(r_max=10, r_step=1,n_cluster=15)
mode_pca,scaler_mode_pca= pca.fit_transform(X)
reconstructed_pca,reconstructed_pcas,err_pca,err_rmse_pca,low_dims_pca,high_dims_pca,all_structure_corrs_pca,all_structure_mi_scores_pca,all_structure_dtws_pca,all_structure_trustworthinesss_pca,rand_scores_pca,nmi_scores_pca,fmi_scores_pca,silhouette_scores_pca,class_scores_pca,transition_matrixs_pca,err_tpms_pca,err_tpm_values_pca,r2s_pca,mses_pca,sampens_pca,hursts_pca,lyaps_pca= pca.inverse_transform(X)

"""
线性降维SVD
"""
#K的选择
rand_scores_svds=[]
nmi_scores_svds=[]
fmi_scores_svds=[]
silhouette_scores_svds=[]
for k in range(5,75,5):
    print(k)
    svd = SVDWZH(r_max=10, r_step=1,n_cluster=k)
    mode_svd,scaler_mode_svd= svd.fit_transform(X)
    reconstructed_svd,reconstructed_svds,err_svd,err_rmse_svd,low_dims_svd,high_dims_svd,all_structure_corrs_svd,all_structure_mi_scores_svd,all_structure_dtws_svd,all_structure_trustworthinesss_svd,rand_scores_svd,nmi_scores_svd,fmi_scores_svd,silhouette_scores_svd,class_scores_svd,transition_matrixs_svd,err_tpms_svd,err_tpm_values_svd,r2s_svd,mses_svd,sampens_svd,hursts_svd,lyaps_svd= svd.inverse_transform(X)
    rand_scores_svds.append(rand_scores_svd)
    nmi_scores_svds.append(nmi_scores_svd)
    fmi_scores_svds.append(fmi_scores_svd)
    silhouette_scores_svds.append(silhouette_scores_svd)
rand_scores_svds=np.array(rand_scores_svds)
nmi_scores_svds=np.array(nmi_scores_svds)
fmi_scores_svds=np.array(fmi_scores_svds)
silhouette_scores_svds=np.array(silhouette_scores_svds)

svd = SVDWZH(r_max=10, r_step=1,n_cluster=15)
mode_svd,scaler_mode_svd= svd.fit_transform(X)
reconstructed_svd,reconstructed_svds,err_svd,err_rmse_svd,low_dims_svd,high_dims_svd,all_structure_corrs_svd,all_structure_mi_scores_svd,all_structure_dtws_svd,all_structure_trustworthinesss_svd,rand_scores_svd,nmi_scores_svd,fmi_scores_svd,silhouette_scores_svd,class_scores_svd,transition_matrixs_svd,err_tpms_svd,err_tpm_values_svd,r2s_svd,mses_svd,sampens_svd,hursts_svd,lyaps_svd= svd.inverse_transform(X)

"""
线性降维ICA
"""
#K的选择
rand_scores_icas=[]
nmi_scores_icas=[]
fmi_scores_icas=[]
silhouette_scores_icas=[]
for k in range(5,75,5):
    print(k)
    ica = ICAWZH(r_max=10, r_step=1,n_cluster=k)
    mode_ica,scaler_mode_ica= ica.fit_transform(X)
    reconstructed_ica,reconstructed_icas,err_ica,err_rmse_ica,low_dims_ica,high_dims_ica,all_structure_corrs_ica,all_structure_mi_scores_ica,all_structure_dtws_ica,all_structure_trustworthinesss_ica,rand_scores_ica,nmi_scores_ica,fmi_scores_ica,silhouette_scores_ica,class_scores_ica,transition_matrixs_ica,err_tpms_ica,err_tpm_values_ica,r2s_ica,mses_ica,sampens_ica,hursts_ica,lyaps_ica= ica.inverse_transform(X)
    rand_scores_icas.append(rand_scores_ica)
    nmi_scores_icas.append(nmi_scores_ica)
    fmi_scores_icas.append(fmi_scores_ica)
    silhouette_scores_icas.append(silhouette_scores_ica)
rand_scores_icas=np.array(rand_scores_icas)
nmi_scores_icas=np.array(nmi_scores_icas)
fmi_scores_icas=np.array(fmi_scores_icas)
silhouette_scores_icas=np.array(silhouette_scores_icas)

ica = ICAWZH(r_max=10, r_step=1,n_cluster=15)
mode_ica,scaler_mode_ica= ica.fit_transform(X)
reconstructed_ica,reconstructed_icas,err_ica,err_rmse_ica,low_dims_ica,high_dims_ica,all_structure_corrs_ica,all_structure_mi_scores_ica,all_structure_dtws_ica,all_structure_trustworthinesss_ica,rand_scores_ica,nmi_scores_ica,fmi_scores_ica,silhouette_scores_ica,class_scores_ica,transition_matrixs_ica,err_tpms_ica,err_tpm_values_ica,r2s_ica,mses_ica,sampens_ica,hursts_ica,lyaps_ica= ica.inverse_transform(X)

"""
线性降维NMF
"""
#K的选择
from sklearn.preprocessing import MinMaxScaler#标准化函数
transfer=MinMaxScaler()#标准化函数Z-score标准化方法
X_NMF = transfer.fit_transform(X) 

rand_scores_nmfs=[]
nmi_scores_nmfs=[]
fmi_scores_nmfs=[]
silhouette_scores_nmfs=[]
for k in range(5,75,5):
    print(k)
    nmf = NMFWZH(r_max=10, r_step=1,n_cluster=k)
    mode_nmf,scaler_mode_nmf= nmf.fit_transform(X_NMF)
    reconstructed_nmf,reconstructed_nmfs,err_nmf,err_rmse_nmf,low_dims_nmf,high_dims_nmf,all_structure_corrs_nmf,all_structure_mi_scores_nmf,all_structure_dtws_nmf,all_structure_trustworthinesss_nmf,rand_scores_nmf,nmi_scores_nmf,fmi_scores_nmf,silhouette_scores_nmf,class_scores_nmf,transition_matrixs_nmf,err_tpms_nmf,err_tpm_values_nmf,r2s_nmf,mses_nmf,sampens_nmf,hursts_nmf,lyaps_nmf= nmf.inverse_transform(X_NMF)
    rand_scores_nmfs.append(rand_scores_nmf)
    nmi_scores_nmfs.append(nmi_scores_nmf)
    fmi_scores_nmfs.append(fmi_scores_nmf)
    silhouette_scores_nmfs.append(silhouette_scores_nmf)
rand_scores_nmfs=np.array(rand_scores_nmfs)
nmi_scores_nmfs=np.array(nmi_scores_nmfs)
fmi_scores_nmfs=np.array(fmi_scores_nmfs)
silhouette_scores_nmfs=np.array(silhouette_scores_nmfs)

nmf = NMFWZH(r_max=10, r_step=1,n_cluster=15)
mode_nmf,scaler_mode_nmf= nmf.fit_transform(X_NMF)
reconstructed_nmf,reconstructed_nmfs,err_nmf,err_rmse_nmf,low_dims_nmf,high_dims_nmf,all_structure_corrs_nmf,all_structure_mi_scores_nmf,all_structure_dtws_nmf,all_structure_trustworthinesss_nmf,rand_scores_nmf,nmi_scores_nmf,fmi_scores_nmf,silhouette_scores_nmf,class_scores_nmf,transition_matrixs_nmf,err_tpms_nmf,err_tpm_values_nmf,r2s_nmf,mses_nmf,sampens_nmf,hursts_nmf,lyaps_nmf= nmf.inverse_transform(X_NMF)

"""
非线性降维KPCA
"""
#参数gamma,alpha的选择
from sklearn.decomposition import KernelPCA
err_recs_kpca = []
gammas = [0.0001, 0.001, 0.01, 0.1, 0.5, 1, 5, 10, 15, 20, 30, 40, 50, 70, 100]
alphas = [0.0001, 0.001, 0.01, 0.1, 0.5, 1]
best_error = float('inf')
best_params = {'gamma': None, 'alpha': None}
for gamma in gammas:
    for alpha in alphas:
        kpca = KernelPCA(n_components=10, kernel='rbf', fit_inverse_transform=True,
                         gamma=gamma, alpha=alpha, n_jobs=-1)
        X_kpca = kpca.fit(X)
        X_fit_transform = X_kpca.fit_transform(X)
        X_reconstructed = kpca.inverse_transform(X_fit_transform)
        err_kpca = np.linalg.norm(X - X_reconstructed) / np.linalg.norm(X)
        if err_kpca < best_error:
            best_error = err_kpca
            best_params['gamma'] = gamma
            best_params['alpha'] = alpha
        err_recs_kpca.append(err_kpca)
err_recs_kpca=np.array(err_recs_kpca).reshape(15,6)              
print(f"Best gamma: {best_params['gamma']}")
print(f"Best alpha: {best_params['alpha']}")
print(f"Best reconstruction error: {best_error}")

#参数k的选择
rand_scores_kpcas=[]
nmi_scores_kpcas=[]
fmi_scores_kpcas=[]
silhouette_scores_kpcas=[]
for k in range(5,75,5):
    print(k)
    kpca = KPCAWZH(r_max=10, r_step=1, kernel_fcn='rbf', gamma=0.1, alpha=0.0001,n_cluster=k)#时间
    mode_kpca,scaler_mode_kpca= kpca.fit_transform(X)
    reconstructed_kpca,reconstructed_kpcas,err_kpca,err_rmse_kpca,low_dims_kpca,high_dims_kpca,all_structure_corrs_kpca,all_structure_mi_scores_kpca,all_structure_dtws_kpca,all_structure_trustworthinesss_kpca,rand_scores_kpca,nmi_scores_kpca,fmi_scores_kpca,silhouette_scores_kpca,class_scores_kpca,transition_matrixs_kpca,err_tpms_kpca,err_tpm_values_kpca,r2s_kpca,mses_kpca,sampens_kpca,hursts_kpca,lyaps_kpca= kpca.inverse_transform(X)
    rand_scores_kpcas.append(rand_scores_kpca)
    nmi_scores_kpcas.append(nmi_scores_kpca)
    fmi_scores_kpcas.append(fmi_scores_kpca)
    silhouette_scores_kpcas.append(silhouette_scores_kpca)
rand_scores_kpcas=np.array(rand_scores_kpcas)
nmi_scores_kpcas=np.array(nmi_scores_kpcas)
fmi_scores_kpcas=np.array(fmi_scores_kpcas)
silhouette_scores_kpcas=np.array(silhouette_scores_kpcas)

kpca = KPCAWZH(r_max=10, r_step=1, kernel_fcn='rbf', gamma=0.1, alpha=0.0001,n_cluster=15)#时间
mode_kpca,scaler_mode_kpca= kpca.fit_transform(X)
reconstructed_kpca,reconstructed_kpcas,err_kpca,err_rmse_kpca,low_dims_kpca,high_dims_kpca,all_structure_corrs_kpca,all_structure_mi_scores_kpca,all_structure_dtws_kpca,all_structure_trustworthinesss_kpca,rand_scores_kpca,nmi_scores_kpca,fmi_scores_kpca,silhouette_scores_kpca,class_scores_kpca,transition_matrixs_kpca,err_tpms_kpca,err_tpm_values_kpca,r2s_kpca,mses_kpca,sampens_kpca,hursts_kpca,lyaps_kpca= kpca.inverse_transform(X)

"""
非线性降维ISOMAP
"""
#参数gamma,alpha的选择
knns = range(5, 75, 5) 
err_recs_isomap = []
best_error = float('inf')
best_params = {'knn': None, 'reg': None}
for knn in knns:
    isomap = Isomap(n_components=10, n_neighbors=knn, n_jobs=16)  # Use knn and reg correctly
    Y = isomap.fit_transform(X)
    W = barycenter_kneighbors_graph(Y, n_neighbors=knn, reg=1e-9, n_jobs=16)  # Correct knn variable used
    X_reconstructed = W @ X
    err_isomap = np.linalg.norm(X - X_reconstructed) / np.linalg.norm(X)
    if err_isomap < best_error:
        best_error = err_isomap
        best_params['knn'] = knn
    err_recs_isomap.append(err_isomap)
err_recs_isomap = np.array(err_recs_isomap).reshape(len(knns))
print(f"Best knn: {best_params['knn']}")
print(f"Best reconstruction error: {best_error}")

#参数k的选择
rand_scores_isomaps=[]
nmi_scores_isomaps=[]
fmi_scores_isomaps=[]
silhouette_scores_isomaps=[]
for k in range(5,75,5):
    print(k)
    isomap = ISOMAPWZH(r_max=10, r_step=1,k_NN=20, n_cluster=k)#时间
    mode_isomap,scaler_mode_isomap= isomap.fit_transform(X)
    reconstructed_isomap,reconstructed_isomaps,err_isomap,err_rmse_isomap,low_dims_isomap,high_dims_isomap,all_structure_corrs_isomap,all_structure_mi_scores_isomap,all_structure_dtws_isomap,all_structure_trustworthinesss_isomap,rand_scores_isomap,nmi_scores_isomap,fmi_scores_isomap,silhouette_scores_isomap,class_scores_isomap,transition_matrixs_isomap,err_tpms_isomap,err_tpm_values_isomap,r2s_isomap,mses_isomap,sampens_isomap,hursts_isomap,lyaps_isomap= isomap.inverse_transform(X)
    rand_scores_isomaps.append(rand_scores_isomap)
    nmi_scores_isomaps.append(nmi_scores_isomap)
    fmi_scores_isomaps.append(fmi_scores_isomap)
    silhouette_scores_isomaps.append(silhouette_scores_isomap)
rand_scores_isomaps=np.array(rand_scores_isomaps)
nmi_scores_isomaps=np.array(nmi_scores_isomaps)
fmi_scores_isomaps=np.array(fmi_scores_isomaps)
silhouette_scores_isomaps=np.array(silhouette_scores_isomaps)

isomap = ISOMAPWZH(r_max=10, r_step=1,k_NN=20, n_cluster=15)#时间
mode_isomap,scaler_mode_isomap= isomap.fit_transform(X)
reconstructed_isomap,reconstructed_isomaps,err_isomap,err_rmse_isomap,low_dims_isomap,high_dims_isomap,all_structure_corrs_isomap,all_structure_mi_scores_isomap,all_structure_dtws_isomap,all_structure_trustworthinesss_isomap,rand_scores_isomap,nmi_scores_isomap,fmi_scores_isomap,silhouette_scores_isomap,class_scores_isomap,transition_matrixs_isomap,err_tpms_isomap,err_tpm_values_isomap,r2s_isomap,mses_isomap,sampens_isomap,hursts_isomap,lyaps_isomap= isomap.inverse_transform(X)

"""
非线性降维LLE
"""

#参数gamma,alpha的选择
knns = range(5, 75, 5)  # Correcting the knns variable
regs = [1e-15,1e-12,1e-9, 1e-6, 1e-3, 1e-2, 1e-1, 1]
err_recs_lle = []
best_error = float('inf')
best_params = {'knn': None, 'reg': None}
for knn in knns:
    for reg in regs:
        lle = LocallyLinearEmbedding(n_components=10, n_neighbors=knn, reg=reg, n_jobs=16)  # Use knn and reg correctly
        Y = lle.fit_transform(X)
        W = barycenter_kneighbors_graph(Y, n_neighbors=knn, reg=reg, n_jobs=16)  # Correct knn variable used
        X_reconstructed = W @ X
        err_lle = np.linalg.norm(X - X_reconstructed) / np.linalg.norm(X)
        if err_lle < best_error:
            best_error = err_lle
            best_params['knn'] = knn
            best_params['reg'] = reg
        err_recs_lle.append(err_lle)
err_recs_lle = np.array(err_recs_lle).reshape(len(knns), len(regs))
print(f"Best knn: {best_params['knn']}")
print(f"Best reg: {best_params['reg']}")
print(f"Best reconstruction error: {best_error}")

#参数k的选择
rand_scores_lles=[]
nmi_scores_lles=[]
fmi_scores_lles=[]
silhouette_scores_lles=[]
for k in range(5,75,5):
    print(k)
    lle = LLEWZH(r_max=10, r_step=1,k_NN=15,reg=1e-9, n_cluster=k)#时间
    mode_lle,scaler_mode_lle= lle.fit_transform(X)
    reconstructed_lle,reconstructed_lles,err_lle,err_rmse_lle,low_dims_lle,high_dims_lle,all_structure_corrs_lle,all_structure_mi_scores_lle,all_structure_dtws_lle,all_structure_trustworthinesss_lle,rand_scores_lle,nmi_scores_lle,fmi_scores_lle,silhouette_scores_lle,class_scores_lle,transition_matrixs_lle,err_tpms_lle,err_tpm_values_lle,r2s_lle,mses_lle,sampens_lle,hursts_lle,lyaps_lle= lle.inverse_transform(X)
    rand_scores_lles.append(rand_scores_lle)
    nmi_scores_lles.append(nmi_scores_lle)
    fmi_scores_lles.append(fmi_scores_lle)
    silhouette_scores_lles.append(silhouette_scores_lle)
rand_scores_lles=np.array(rand_scores_lles)
nmi_scores_lles=np.array(nmi_scores_lles)
fmi_scores_lles=np.array(fmi_scores_lles)
silhouette_scores_lles=np.array(silhouette_scores_lles)

lle = LLEWZH(r_max=10, r_step=1,k_NN=15,reg=1e-9, n_cluster=15)#时间
mode_lle,scaler_mode_lle= lle.fit_transform(X)
reconstructed_lle,reconstructed_lles,err_lle,err_rmse_lle,low_dims_lle,high_dims_lle,all_structure_corrs_lle,all_structure_mi_scores_lle,all_structure_dtws_lle,all_structure_trustworthinesss_lle,rand_scores_lle,nmi_scores_lle,fmi_scores_lle,silhouette_scores_lle,class_scores_lle,transition_matrixs_lle,err_tpms_lle,err_tpm_values_lle,r2s_lle,mses_lle,sampens_lle,hursts_lle,lyaps_lle= lle.inverse_transform(X)

"""
非线性降维LEM
"""

#参数gamma,alpha的选择
knns = range(5, 75, 5)
regs = [1e-15, 1e-12, 1e-9, 1e-6, 1e-3, 1e-2, 1e-1, 1]
gammas = [0.0001, 0.001, 0.01, 0.1, 0.5, 1, 5, 10, 15, 20, 30, 40, 50, 70, 100]
err_recs_lle = []
best_error = float('inf')
best_params = {'knn': None, 'reg': None, 'gamma': None}
for knn in knns:
    for reg in regs:
        for gamma in gammas:
            affinity_knn = kneighbors_graph(X, knn-1, include_self=False)
            affinity_knn = np.ceil(0.5 * (affinity_knn + affinity_knn.T).todense())  # Symmetrize and convert to dense
            affinity_rbf = rbf_kernel(X, gamma=gamma)
            affinity_mat = np.multiply(affinity_knn, affinity_rbf)  
            affinity_mat = affinity_rbf
            lem = SpectralEmbedding(n_components=10, affinity='precomputed', random_state=42, n_jobs=16)
            Y = lem.fit_transform(affinity_mat)  # Correct 'lem' object used
            W = barycenter_kneighbors_graph(Y, n_neighbors=knn, reg=reg, n_jobs=16)  # Use correct knn and reg
            X_reconstructed = W @ X
            err_lle = np.linalg.norm(X - X_reconstructed) / np.linalg.norm(X)
            if err_lle < best_error:
                best_error = err_lle
                best_params['knn'] = knn
                best_params['reg'] = reg
                best_params['gamma'] = gamma 
            err_recs_lle.append(err_lle)
err_recs_lle = np.array(err_recs_lle).reshape(len(knns), len(regs), len(gammas))
print(f"Best knn: {best_params['knn']}")
print(f"Best reg: {best_params['reg']}")
print(f"Best gamma: {best_params['gamma']}")
print(f"Best reconstruction error: {best_error}")

#参数k的选择
rand_scores_lems=[]
nmi_scores_lems=[]
fmi_scores_lems=[]
silhouette_scores_lems=[]
for k in range(5,75,5):
    print(k)
    lem = LEMWZH(r_max=10, r_step=1,k_NN=20,reg=1e-15, gamma=0.01, n_cluster=k)#时间
    mode_lem,scaler_mode_lem= lem.fit_transform(X)
    reconstructed_lem,reconstructed_lems,err_lem,err_rmse_lem,low_dims_lem,high_dims_lem,all_structure_corrs_lem,all_structure_mi_scores_lem,all_structure_dtws_lem,all_structure_trustworthinesss_lem,rand_scores_lem,nmi_scores_lem,fmi_scores_lem,silhouette_scores_lem,class_scores_lem,transition_matrixs_lem,err_tpms_lem,err_tpm_values_lem,r2s_lem,mses_lem,sampens_lem,hursts_lem,lyaps_lem= lem.inverse_transform(X)
    rand_scores_lems.append(rand_scores_lem)
    nmi_scores_lems.append(nmi_scores_lem)
    fmi_scores_lems.append(fmi_scores_lem)
    silhouette_scores_lems.append(silhouette_scores_lem)
rand_scores_lems=np.array(rand_scores_lems)
nmi_scores_lems=np.array(nmi_scores_lems)
fmi_scores_lems=np.array(fmi_scores_lems)
silhouette_scores_lems=np.array(silhouette_scores_lems)

lem = LEMWZH(r_max=10, r_step=1,k_NN=20,reg=1e-15, gamma=0.01,  n_cluster=15)#时间
mode_lem,scaler_mode_lem= lem.fit_transform(X)
reconstructed_lem,reconstructed_lems,err_lem,err_rmse_lem,low_dims_lem,high_dims_lem,all_structure_corrs_lem,all_structure_mi_scores_lem,all_structure_dtws_lem,all_structure_trustworthinesss_lem,rand_scores_lem,nmi_scores_lem,fmi_scores_lem,silhouette_scores_lem,class_scores_lem,transition_matrixs_lem,err_tpms_lem,err_tpm_values_lem,r2s_lem,mses_lem,sampens_lem,hursts_lem,lyaps_lem= lem.inverse_transform(X)

from AEWZH2024 import AEWZH
# #参数k的选择
# rand_scores_aes=[]
# nmi_scores_aes=[]
# fmi_scores_aes=[]
# silhouette_scores_aes=[]
# for k in range(5,75,5):
#     print(k)
#     ae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='AE',input_dim=4000,n_cluster=k)
#     mode_ae,scaler_mode_ae,reconstructed_ae,reconstructed_aes,err_ae,err_rmse_ae,low_dims_ae,high_dims_ae,all_structure_corrs_ae,all_structure_mi_scores_ae,all_structure_dtws_ae,all_structure_trustworthinesss_ae,rand_scores_ae,nmi_scores_ae,fmi_scores_ae,silhouette_scores_ae,class_scores_ae,transition_matrixs_ae,err_tpms_ae,err_tpm_values_ae,r2s_ae,mses_ae,sampens_ae,hursts_ae,lyaps_ae= ae.fit_transform(X)
#     rand_scores_aes.append(rand_scores_ae)
#     nmi_scores_aes.append(nmi_scores_ae)
#     fmi_scores_aes.append(fmi_scores_ae)
#     silhouette_scores_aes.append(silhouette_scores_ae)
# rand_scores_aes=np.array(rand_scores_aes)
# nmi_scores_aes=np.array(nmi_scores_aes)
# fmi_scores_aes=np.array(fmi_scores_aes)
# silhouette_scores_aes=np.array(silhouette_scores_aes)
ae = AEWZH(r_max=8, r_step=1, batch_size=201, lr=1e-3,model_type='AE',input_dim=30189, n_cluster=15)
mode_ae,scaler_mode_ae,reconstructed_ae,reconstructed_aes,err_ae,err_rmse_ae,low_dims_ae,high_dims_ae,all_structure_corrs_ae,all_structure_mi_scores_ae,all_structure_dtws_ae,all_structure_trustworthinesss_ae,rand_scores_ae,nmi_scores_ae,fmi_scores_ae,silhouette_scores_ae,class_scores_ae,transition_matrixs_ae,err_tpms_ae,err_tpm_values_ae,r2s_ae,mses_ae,sampens_ae,hursts_ae,lyaps_ae,hidden_layers_encoder_PCAae,hidden_layers_decoder_PCAae= ae.fit_transform(X)

# """
# 非线性降维VAE
# """
# #参数k的选择
# # rand_scores_vaes=[]
# # nmi_scores_vaes=[]
# # fmi_scores_vaes=[]
# # silhouette_scores_vaes=[]
# # for k in range(5,75,5):
# #     print(k)
# #     vae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='VAE',input_dim=4000,n_cluster=k)
# #     mode_vae,scaler_mode_vae,reconstructed_vae,reconstructed_vaes,err_vae,err_rmse_vae,low_dims_vae,high_dims_vae,all_structure_corrs_vae,all_structure_mi_scores_vae,all_structure_dtws_vae,all_structure_trustworthinesss_vae,rand_scores_vae,nmi_scores_vae,fmi_scores_vae,silhouette_scores_vae,class_scores_vae,transition_matrixs_vae,err_tpms_vae,err_tpm_values_vae,r2s_vae,mses_vae,sampens_vae,hursts_vae,lyaps_vae= vae.fit_transform(X)
# #     rand_scores_vaes.append(rand_scores_vae)
# #     nmi_scores_vaes.append(nmi_scores_vae)
# #     fmi_scores_vaes.append(fmi_scores_vae)
# #     silhouette_scores_vaes.append(silhouette_scores_vae)
# # rand_scores_vaes=np.array(rand_scores_vaes)
# # nmi_scores_vaes=np.array(nmi_scores_vaes)
# # fmi_scores_vaes=np.array(fmi_scores_vaes)
# # silhouette_scores_vaes=np.array(silhouette_scores_vaes)

vae = AEWZH(r_max=8, r_step=1, batch_size=201, lr=1e-3,model_type='VAE',input_dim=30189, n_cluster=15)
mode_vae,scaler_mode_vae,reconstructed_vae,reconstructed_vaes,err_vae,err_rmse_vae,low_dims_vae,high_dims_vae,all_structure_corrs_vae,all_structure_mi_scores_vae,all_structure_dtws_vae,all_structure_trustworthinesss_vae,rand_scores_vae,nmi_scores_vae,fmi_scores_vae,silhouette_scores_vae,class_scores_vae,transition_matrixs_vae,err_tpms_vae,err_tpm_values_vae,r2s_vae,mses_vae,sampens_vae,hursts_vae,lyaps_vae= vae.fit_transform(X)

# """
# 非线性降维KAE
# """
# #参数k的选择
# # rand_scores_kaes=[]
# # nmi_scores_kaes=[]
# # fmi_scores_kaes=[]
# # silhouette_scores_kaes=[]
# # for k in range(5,75,5):
# #     print(k)
# #     kae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='KAE',input_dim=4000,n_cluster=k)
# #     mode_kae,scaler_mode_kae,reconstructed_kae,reconstructed_kaes,err_kae,err_rmse_kae,low_dims_kae,high_dims_kae,all_structure_corrs_kae,all_structure_mi_scores_kae,all_structure_dtws_kae,all_structure_trustworthinesss_kae,rand_scores_kae,nmi_scores_kae,fmi_scores_kae,silhouette_scores_kae,class_scores_kae,transition_matrixs_kae,err_tpms_kae,err_tpm_values_kae,r2s_kae,mses_kae,sampens_kae,hursts_kae,lyaps_kae= kae.fit_transform(X)
# #     rand_scores_kaes.append(rand_scores_kae)
# #     nmi_scores_kaes.append(nmi_scores_kae)
# #     fmi_scores_kaes.append(fmi_scores_kae)
# #     silhouette_scores_kaes.append(silhouette_scores_kae)
# # rand_scores_kaes=np.array(rand_scores_kaes)
# # nmi_scores_kaes=np.array(nmi_scores_kaes)
# # fmi_scores_kaes=np.array(fmi_scores_kaes)
# # silhouette_scores_kaes=np.array(silhouette_scores_kaes)

# kae = AEWZH(r_max=10, r_step=1, batch_size=401, lr=1e-3,model_type='KAE',input_dim=4000, n_cluster=15)
# mode_kae,scaler_mode_kae,reconstructed_kae,reconstructed_kaes,err_kae,err_rmse_kae,low_dims_kae,high_dims_kae,all_structure_corrs_kae,all_structure_mi_scores_kae,all_structure_dtws_kae,all_structure_trustworthinesss_kae,rand_scores_kae,nmi_scores_kae,fmi_scores_kae,silhouette_scores_kae,class_scores_kae,transition_matrixs_kae,err_tpms_kae,err_tpm_values_kae,r2s_kae,mses_kae,sampens_kae,hursts_kae,lyaps_kae= kae.fit_transform(X)

# """
# 非线性降维KVAE
# """
# #参数k的选择
# # rand_scores_kvaes=[]
# # nmi_scores_kvaes=[]
# # fmi_scores_kvaes=[]
# # silhouette_scores_kvaes=[]
# # for k in range(5,75,5):
# #     print(k)
# #     kvae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='KVAE',input_dim=4000,n_cluster=k)
# #     mode_kvae,scaler_mode_kvae,reconstructed_kvae,reconstructed_kvaes,err_kvae,err_rmse_kvae,low_dims_kvae,high_dims_kvae,all_structure_corrs_kvae,all_structure_mi_scores_kvae,all_structure_dtws_kvae,all_structure_trustworthinesss_kvae,rand_scores_kvae,nmi_scores_kvae,fmi_scores_kvae,silhouette_scores_kvae,class_scores_kvae,transition_matrixs_kvae,err_tpms_kvae,err_tpm_values_kvae,r2s_kvae,mses_kvae,sampens_kvae,hursts_kvae,lyaps_kvae= kvae.fit_transform(X)
# #     rand_scores_kvaes.append(rand_scores_kvae)
# #     nmi_scores_kvaes.append(nmi_scores_kvae)
# #     fmi_scores_kvaes.append(fmi_scores_kvae)
# #     silhouette_scores_kvaes.append(silhouette_scores_kvae)
# # rand_scores_kvaes=np.array(rand_scores_kvaes)
# # nmi_scores_kvaes=np.array(nmi_scores_kvaes)
# # fmi_scores_kvaes=np.array(fmi_scores_kvaes)
# # silhouette_scores_kvaes=np.array(silhouette_scores_kvaes)
# kvae = AEWZH(r_max=10, r_step=1, batch_size=128, lr=1e-3,model_type='KVAE',input_dim=4000, n_cluster=15)
# mode_kvae,scaler_mode_kvae,reconstructed_kvae,reconstructed_kvaes,err_kvae,err_rmse_kvae,low_dims_kvae,high_dims_kvae,all_structure_corrs_kvae,all_structure_mi_scores_kvae,all_structure_dtws_kvae,all_structure_trustworthinesss_kvae,rand_scores_kvae,nmi_scores_kvae,fmi_scores_kvae,silhouette_scores_kvae,class_scores_kvae,transition_matrixs_kvae,err_tpms_kvae,err_tpm_values_kvae,r2s_kvae,mses_kvae,sampens_kvae,hursts_kvae,lyaps_kvae= kvae.fit_transform(X)
