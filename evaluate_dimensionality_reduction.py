# ================================ 降维方法评价示例 (Enhanced) ===================================
"""
降维方法评价示例脚本 - 增强版
展示如何使用完善后的DimensionalityReductionEvaluator评价不同的降维方法

新增功能:
1. 稳定性和鲁棒性评价
2. 计算效率评价
3. 可解释性评价
4. 多尺度结构评价
5. 统计显著性检验
6. 综合评分机制

作者: 基于原版本改进
版本: 2.0 Enhanced
"""

import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA, KernelPCA, FastICA, NMF
from sklearn.manifold import TSNE, MDS, Isomap, LocallyLinearEmbedding
from sklearn.preprocessing import StandardScaler
from dimensionality_reduction_evaluator import DimensionalityReductionEvaluator
import time
import pandas as pd
import os
import seaborn as sns
import warnings

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 忽略警告
warnings.filterwarnings('ignore')

def load_data(data_path):
    """
    加载数据
    参数:
    data_path: 数据路径
    返回:
    X: 数据矩阵
    """
    print(f"加载数据: {data_path}")
    # 这里根据实际数据格式进行修改
    data = np.load(data_path) if data_path.endswith('.npy') else pd.read_csv(data_path).values
    
    # 标准化数据
    scaler = StandardScaler()
    X = scaler.fit_transform(data)
    
    print(f"数据形状: {X.shape}")
    return X

def apply_dimensionality_reduction(X, method, n_components):
    """
    应用降维方法
    参数:
    X: 数据矩阵
    method: 降维方法
    n_components: 降维维数
    返回:
    X_reduced: 降维后的数据
    X_reconstructed: 重构的数据
    """
    start_time = time.time()
    
    if method == 'PCA':
        model = PCA(n_components=n_components)
        X_reduced = model.fit_transform(X)
        X_reconstructed = model.inverse_transform(X_reduced)
    
    elif method == 'KernelPCA':
        model = KernelPCA(n_components=n_components, kernel='rbf', fit_inverse_transform=True)
        X_reduced = model.fit_transform(X)
        X_reconstructed = model.inverse_transform(X_reduced)
    
    elif method == 'FastICA':
        model = FastICA(n_components=n_components, random_state=42)
        X_reduced = model.fit_transform(X)
        X_reconstructed = model.inverse_transform(X_reduced)
    
    elif method == 'NMF':
        # 确保数据非负
        X_nonneg = X - X.min()
        model = NMF(n_components=n_components, init='random', random_state=42)
        X_reduced = model.fit_transform(X_nonneg)
        X_reconstructed = model.inverse_transform(X_reduced)
        # 调整回原始范围
        X_reconstructed = X_reconstructed + X.min()
    
    elif method == 'TSNE':
        model = TSNE(n_components=n_components, random_state=42)
        X_reduced = model.fit_transform(X)
        # t-SNE没有直接的逆变换，使用原始数据
        X_reconstructed = X
    
    elif method == 'MDS':
        model = MDS(n_components=n_components, random_state=42)
        X_reduced = model.fit_transform(X)
        # MDS没有直接的逆变换，使用原始数据
        X_reconstructed = X
    
    elif method == 'Isomap':
        model = Isomap(n_components=n_components, n_neighbors=10)
        X_reduced = model.fit_transform(X)
        # Isomap没有直接的逆变换，使用原始数据
        X_reconstructed = X
    
    elif method == 'LLE':
        model = LocallyLinearEmbedding(n_components=n_components, n_neighbors=10, random_state=42)
        X_reduced = model.fit_transform(X)
        # LLE没有直接的逆变换，使用原始数据
        X_reconstructed = X
    
    else:
        raise ValueError(f"未知的降维方法: {method}")
    
    end_time = time.time()
    print(f"{method} 降维耗时: {end_time - start_time:.2f}秒")
    
    return X_reduced, X_reconstructed

def evaluate_methods_comprehensive(X, methods, n_components, evaluator):
    """
    使用综合评价方法评价不同降维方法 (Enhanced Version)

    参数:
        X (np.ndarray): 数据矩阵
        methods (list): 降维方法名称列表
        n_components (int): 降维维数
        evaluator (DimensionalityReductionEvaluator): 评价器实例

    返回:
        tuple: (evaluation_results, summary_df, statistical_tests)
        - evaluation_results: 详细评价结果列表
        - summary_df: 汇总结果DataFrame
        - statistical_tests: 统计显著性检验结果
    """
    print("=" * 80)
    print("开始综合评价降维方法")
    print("=" * 80)

    evaluation_results = []

    for method_name in methods:
        print(f"\n{'='*20} 评价 {method_name} {'='*20}")

        try:
            # 应用降维方法
            X_reduced, X_reconstructed, method_obj, has_inverse_transform = apply_dimensionality_reduction_enhanced(
                X, method_name, n_components
            )

            # 使用综合评价方法
            result = evaluator.comprehensive_evaluate(
                X_original=X,
                X_reduced=X_reduced,
                X_reconstructed=X_reconstructed,
                reduction_method=method_obj,
                method_name=method_name,
                has_inverse_transform=has_inverse_transform
            )

            evaluation_results.append(result)

        except Exception as e:
            print(f"评价 {method_name} 时出错: {e}")
            # 创建默认结果
            default_result = {
                'method_name': method_name,
                'comprehensive_evaluation': {
                    'overall_score': 0.0,
                    'score_breakdown': {},
                    'weights_used': evaluator.weights.copy()
                },
                'error': str(e)
            }
            evaluation_results.append(default_result)

    # 创建汇总DataFrame
    summary_data = []
    for result in evaluation_results:
        row = {
            'Method': result['method_name'],
            'Overall_Score': result['comprehensive_evaluation']['overall_score']
        }

        # 添加各维度分数
        if 'score_breakdown' in result['comprehensive_evaluation']:
            breakdown = result['comprehensive_evaluation']['score_breakdown']
            for key, value in breakdown.items():
                row[key.replace('_', ' ').title()] = value

        # 添加关键指标
        if 'reconstruction_quality' in result:
            row['Reconstruction_Error'] = result['reconstruction_quality'].get('frobenius_error', np.nan)
        if 'structure_preservation' in result:
            row['Trustworthiness'] = result['structure_preservation'].get('trustworthiness', np.nan)
        if 'clustering_performance' in result:
            row['Clustering_Score'] = result['clustering_performance'].get('clustering_score', np.nan)
        if 'efficiency' in result:
            row['Time_Cost'] = result['efficiency'].get('time_cost', np.nan)
            row['Memory_Usage'] = result['efficiency'].get('memory_usage', np.nan)

        summary_data.append(row)

    summary_df = pd.DataFrame(summary_data)

    # 进行统计显著性检验
    print(f"\n{'='*20} 统计显著性检验 {'='*20}")
    statistical_tests = evaluator.statistical_significance_test(evaluation_results)

    # 打印统计检验结果
    if 'overall' in statistical_tests:
        overall_test = statistical_tests['overall']
        if 'p_value' in overall_test:
            print(f"整体差异显著性检验: p-value = {overall_test['p_value']:.6f}")
            if overall_test['significant']:
                print("✓ 方法间存在显著差异")
                print(f"最佳方法: {overall_test.get('best_method', 'Unknown')}")
                print(f"最差方法: {overall_test.get('worst_method', 'Unknown')}")
            else:
                print("✗ 方法间无显著差异")

    print(f"\n{'='*20} 评价完成 {'='*20}")
    return evaluation_results, summary_df, statistical_tests


def apply_dimensionality_reduction_enhanced(X, method_name, n_components):
    """
    增强版降维方法应用函数 - 正确处理无逆变换的方法

    参数:
        X (np.ndarray): 输入数据
        method_name (str): 降维方法名称
        n_components (int): 降维维数

    返回:
        tuple: (X_reduced, X_reconstructed, method_object, has_inverse_transform)
        - X_reduced: 降维后的数据
        - X_reconstructed: 重构数据（无逆变换时为None）
        - method_object: 方法对象
        - has_inverse_transform: 是否支持逆变换
    """
    start_time = time.time()

    # 确保数据是浮点型
    X = X.astype(np.float64)

    # 定义不支持逆变换的方法
    non_invertible_methods = {'TSNE', 'MDS', 'Isomap', 'LLE'}
    has_inverse_transform = method_name not in non_invertible_methods

    try:
        if method_name == 'PCA':
            method = PCA(n_components=n_components, random_state=42)
            X_reduced = method.fit_transform(X)
            X_reconstructed = method.inverse_transform(X_reduced)

        elif method_name == 'KernelPCA':
            method = KernelPCA(n_components=n_components, kernel='rbf',
                             fit_inverse_transform=True, random_state=42)
            X_reduced = method.fit_transform(X)
            try:
                X_reconstructed = method.inverse_transform(X_reduced)
            except:
                print(f"  警告: {method_name} 逆变换失败，设为不支持逆变换")
                has_inverse_transform = False
                X_reconstructed = None

        elif method_name == 'FastICA':
            method = FastICA(n_components=n_components, random_state=42, max_iter=1000)
            X_reduced = method.fit_transform(X)
            X_reconstructed = method.inverse_transform(X_reduced)

        elif method_name == 'NMF':
            # 确保数据非负
            X_nonneg = X - X.min() + 1e-10
            method = NMF(n_components=n_components, init='random', random_state=42, max_iter=1000)
            X_reduced = method.fit_transform(X_nonneg)
            X_reconstructed = method.inverse_transform(X_reduced)
            # 调整回原始范围
            X_reconstructed = X_reconstructed + X.min() - 1e-10

        elif method_name == 'TSNE':
            print(f"  注意: {method_name} 不支持逆变换，重构误差将设为惩罚值 10000")
            method = TSNE(n_components=min(n_components, 3), random_state=42,
                         perplexity=min(30, X.shape[0]-1))
            X_reduced = method.fit_transform(X)
            X_reconstructed = None  # 不支持逆变换
            has_inverse_transform = False

        elif method_name == 'MDS':
            print(f"  注意: {method_name} 不支持逆变换，重构误差将设为惩罚值 10000")
            method = MDS(n_components=n_components, random_state=42, max_iter=1000)
            X_reduced = method.fit_transform(X)
            X_reconstructed = None  # 不支持逆变换
            has_inverse_transform = False

        elif method_name == 'Isomap':
            print(f"  注意: {method_name} 不支持逆变换，重构误差将设为惩罚值 10000")
            n_neighbors = min(10, X.shape[0] - 1)
            method = Isomap(n_components=n_components, n_neighbors=n_neighbors)
            X_reduced = method.fit_transform(X)
            X_reconstructed = None  # 不支持逆变换
            has_inverse_transform = False

        elif method_name == 'LLE':
            print(f"  注意: {method_name} 不支持逆变换，重构误差将设为惩罚值 10000")
            n_neighbors = min(10, X.shape[0] - 1)
            method = LocallyLinearEmbedding(n_components=n_components,
                                          n_neighbors=n_neighbors, random_state=42)
            X_reduced = method.fit_transform(X)
            X_reconstructed = None  # 不支持逆变换
            has_inverse_transform = False

        else:
            raise ValueError(f"未知的降维方法: {method_name}")

        end_time = time.time()
        print(f"  {method_name} 降维耗时: {end_time - start_time:.2f}秒")
        print(f"  支持逆变换: {'是' if has_inverse_transform else '否'}")

        return X_reduced, X_reconstructed, method, has_inverse_transform

    except Exception as e:
        print(f"  {method_name} 降维失败: {e}")
        # 返回默认结果
        X_reduced = X[:, :n_components] if X.shape[1] >= n_components else X
        X_reconstructed = None
        return X_reduced, X_reconstructed, None, False

def plot_results(results, output_dir):
    """
    绘制评价结果
    参数:
    results: 评价结果字典
    output_dir: 输出目录
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 转换为DataFrame
    df = pd.DataFrame(results)
    
    # 保存结果
    df.to_csv(os.path.join(output_dir, 'evaluation_results.csv'), index=False)
    
    # 绘制各项指标的对比图
    metrics = [
        ('reconstruction_error', '重构误差', 'lower'),
        ('rmse', 'RMSE', 'lower'),
        ('correlation', '皮尔逊相关系数', 'higher'),
        ('mutual_info', '互信息', 'higher'),
        ('dtw_distance', 'DTW距离', 'lower'),
        ('trustworthiness', '可信度', 'higher'),
        ('rand_score', '调整兰德指数', 'higher'),
        ('nmi_score', '标准化互信息', 'higher'),
        ('fmi_score', 'Fowlkes-Mallows指数', 'higher'),
        ('silhouette_score', '轮廓系数', 'higher'),
        ('classification_score', '分类准确率', 'higher'),
        ('transition_error', '转移矩阵误差', 'lower'),
        ('sampen_mean', '样本熵均值', 'higher'),
        ('hurst_mean', 'Hurst指数均值', 'higher'),
        ('lyap_mean', '李雅普诺夫指数均值', 'lower'),
        ('r2_score', 'R²分数', 'higher'),
        ('mse_score', 'MSE分数', 'lower')
    ]
    
    for metric, title, direction in metrics:
        plt.figure(figsize=(10, 6))
        
        # 排序
        if direction == 'higher':
            sorted_df = df.sort_values(by=metric, ascending=False)
        else:
            sorted_df = df.sort_values(by=metric, ascending=True)
        
        # 绘制条形图
        ax = sns.barplot(x='method', y=metric, data=sorted_df)
        
        # 添加数值标签
        for i, v in enumerate(sorted_df[metric]):
            ax.text(i, v, f'{v:.4f}', ha='center', va='bottom')
        
        plt.title(f'{title}对比')
        plt.xlabel('降维方法')
        plt.ylabel(title)
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(os.path.join(output_dir, f'{metric}_comparison.png'), dpi=300)
        plt.close()
    
    # 绘制雷达图
    categories = [title for _, title, _ in metrics]
    N = len(categories)
    
    # 计算角度
    angles = [n / float(N) * 2 * np.pi for n in range(N)]
    angles += angles[:1]
    
    # 创建雷达图
    fig, ax = plt.subplots(figsize=(12, 12), subplot_kw=dict(polar=True))
    
    # 标准化数据
    normalized_data = {}
    for metric, _, direction in metrics:
        if direction == 'higher':
            normalized_data[metric] = (df[metric] - df[metric].min()) / (df[metric].max() - df[metric].min() + 1e-10)
        else:
            normalized_data[metric] = 1 - (df[metric] - df[metric].min()) / (df[metric].max() - df[metric].min() + 1e-10)
    
    # 绘制每个方法的雷达图
    for i, method in enumerate(df['method']):
        values = [normalized_data[metric][i] for metric, _, _ in metrics]
        values += values[:1]
        
        ax.plot(angles, values, linewidth=2, linestyle='solid', label=method)
        ax.fill(angles, values, alpha=0.1)
    
    # 设置雷达图属性
    ax.set_theta_offset(np.pi / 2)
    ax.set_theta_direction(-1)
    plt.xticks(angles[:-1], categories)
    plt.yticks([0.2, 0.4, 0.6, 0.8], ['0.2', '0.4', '0.6', '0.8'], color='grey', size=10)
    plt.ylim(0, 1)
    
    # 添加图例
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    plt.title('降维方法综合评价雷达图', size=15, y=1.1)
    
    # 保存雷达图
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'radar_chart.png'), dpi=300)
    plt.close()

def main_enhanced():
    """
    增强版主函数 - 使用新的综合评价体系
    """
    print("降维方法综合评价系统 v2.0")
    print("=" * 60)

    # 数据路径 (请根据实际情况修改)
    data_path = 'your_data_path.csv'  # 替换为实际数据路径

    # 降维方法列表
    methods = ['PCA', 'FastICA', 'NMF', 'TSNE']  # 可以根据需要调整

    # 降维维数
    n_components = 5

    # 创建增强版评价器
    evaluator = DimensionalityReductionEvaluator(
        n_cluster=15,
        max_components=10,
        random_state=42
    )

    # 可选：自定义权重
    custom_weights = {
        'reconstruction_quality': 0.25,
        'structure_preservation': 0.30,
        'clustering_performance': 0.20,
        'classification_performance': 0.15,
        'stability': 0.05,
        'efficiency': 0.03,
        'interpretability': 0.02
    }
    evaluator.set_weights(custom_weights)

    try:
        # 加载数据
        print("正在加载数据...")
        X = load_data(data_path)

        # 进行综合评价
        print("开始综合评价...")
        evaluation_results, summary_df, statistical_tests = evaluate_methods_comprehensive(
            X, methods, n_components, evaluator
        )

        # 保存结果
        output_dir = 'evaluation_results_enhanced'
        os.makedirs(output_dir, exist_ok=True)

        # 保存详细结果
        import json
        with open(os.path.join(output_dir, 'detailed_results.json'), 'w', encoding='utf-8') as f:
            # 处理numpy类型以便JSON序列化
            def convert_numpy(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.bool_):
                    return bool(obj)
                return obj

            json_results = []
            for result in evaluation_results:
                json_result = {}
                for key, value in result.items():
                    if isinstance(value, dict):
                        json_result[key] = {k: convert_numpy(v) for k, v in value.items()}
                    else:
                        json_result[key] = convert_numpy(value)
                json_results.append(json_result)

            json.dump(json_results, f, indent=2, ensure_ascii=False, default=str)

        # 保存汇总表格
        summary_df.to_csv(os.path.join(output_dir, 'summary_results.csv'), index=False)

        # 保存统计检验结果
        with open(os.path.join(output_dir, 'statistical_tests.json'), 'w', encoding='utf-8') as f:
            json.dump(statistical_tests, f, indent=2, ensure_ascii=False, default=str)

        # 打印汇总结果
        print("\n" + "=" * 60)
        print("评价结果汇总:")
        print("=" * 60)
        print(summary_df.round(4))

        # 获取评价历史摘要
        summary = evaluator.get_evaluation_summary()
        print(f"\n最佳方法: {summary.get('best_method', {}).get('name', 'Unknown')} "
              f"(分数: {summary.get('best_method', {}).get('score', 0):.4f})")
        print(f"最差方法: {summary.get('worst_method', {}).get('name', 'Unknown')} "
              f"(分数: {summary.get('worst_method', {}).get('score', 0):.4f})")
        print(f"平均分数: {summary.get('average_score', 0):.4f}")

        # 绘制增强版结果图
        plot_enhanced_results(evaluation_results, summary_df, output_dir)

        print(f"\n所有结果已保存到 {output_dir} 目录")
        print("评价完成!")

    except FileNotFoundError:
        print(f"错误: 找不到数据文件 {data_path}")
        print("请检查文件路径或使用示例数据")

        # 使用示例数据
        print("使用示例数据进行演示...")
        X = generate_sample_data()

        evaluation_results, summary_df, statistical_tests = evaluate_methods_comprehensive(
            X, methods, n_components, evaluator
        )

        print("\n示例评价结果:")
        print(summary_df.round(4))

    except Exception as e:
        print(f"评价过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def generate_sample_data(n_samples=1000, n_features=50, noise_level=0.1):
    """
    生成示例数据用于演示
    """
    print("生成示例数据...")
    np.random.seed(42)

    # 生成具有内在低维结构的高维数据
    # 真实的低维数据
    n_true_components = 3
    Z = np.random.randn(n_samples, n_true_components)

    # 生成随机投影矩阵
    W = np.random.randn(n_features, n_true_components)

    # 投影到高维空间
    X = Z @ W.T

    # 添加噪声
    X += noise_level * np.random.randn(n_samples, n_features)

    # 标准化
    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    print(f"生成数据形状: {X.shape}")
    return X


def plot_enhanced_results(evaluation_results, summary_df, output_dir):
    """
    绘制增强版评价结果
    """
    print("正在生成可视化结果...")

    # 1. 综合分数对比
    plt.figure(figsize=(12, 8))
    methods = summary_df['Method']
    scores = summary_df['Overall_Score']

    bars = plt.bar(methods, scores, color='skyblue', alpha=0.7)
    plt.title('降维方法综合评分对比', fontsize=16, fontweight='bold')
    plt.xlabel('降维方法', fontsize=12)
    plt.ylabel('综合评分', fontsize=12)
    plt.xticks(rotation=45)

    # 添加数值标签
    for bar, score in zip(bars, scores):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{score:.3f}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'comprehensive_scores.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 各维度分数热力图
    score_columns = [col for col in summary_df.columns
                    if col not in ['Method', 'Overall_Score', 'Reconstruction_Error',
                                  'Trustworthiness', 'Clustering_Score', 'Time_Cost', 'Memory_Usage']]

    if score_columns:
        plt.figure(figsize=(12, 8))
        heatmap_data = summary_df[['Method'] + score_columns].set_index('Method')

        sns.heatmap(heatmap_data, annot=True, cmap='RdYlBu_r', center=0.5,
                   fmt='.3f', cbar_kws={'label': '评分'})
        plt.title('各维度评分热力图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'dimension_scores_heatmap.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    print(f"可视化结果已保存到 {output_dir}")


if __name__ == "__main__":
    main_enhanced()
