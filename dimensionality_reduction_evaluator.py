# ================================ 降维方法综合评价模块 ===================================
"""
降维方法综合评价模块 (Enhanced Dimensionality Reduction Evaluator)

本模块提供了一套完整的降维方法评价体系，包含以下七大评价维度：
1. 重构质量评价 - 衡量数据重构的准确性
2. 结构保持性评价 - 评估降维后数据结构的保持程度
3. 聚类性能评价 - 测试降维数据的聚类效果
4. 分类性能评价 - 评估降维数据的分类能力
5. 稳定性和鲁棒性评价 - 测试方法对噪声和参数变化的敏感性
6. 计算效率评价 - 评估时间和空间复杂度
7. 可解释性评价 - 衡量降维结果的可解释程度

作者：基于PCAWZH类改进和扩展
版本：2.0 Enhanced
更新日期：2025年
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import tracemalloc
import psutil
import warnings
from sklearn.metrics import mean_squared_error, pairwise_distances, accuracy_score
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from scipy import stats
from scipy.stats import friedmanchisquare, wilcoxon
from sklearn.feature_selection import mutual_info_regression
from sklearn.metrics import mutual_info_score
from sklearn.manifold import trustworthiness
from dtw import dtw
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics.cluster import adjusted_rand_score, adjusted_mutual_info_score
from sklearn.metrics import normalized_mutual_info_score, fowlkes_mallows_score
from sklearn import metrics
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import PolynomialFeatures
from sklearn.metrics import r2_score, mean_absolute_percentage_error
import nolds
from Us_kmeans import Us_KMeans

# 忽略警告信息
warnings.filterwarnings('ignore')


# ================================ 辅助函数 ===================================

def sum_above_diagonal(matrix):
    """
    计算矩阵对角线及以上元素的和

    参数:
        matrix (np.ndarray): 输入的方阵

    返回:
        float: 对角线及以上元素的总和

    用途:
        用于量化转移矩阵的误差，主要关注上三角和对角线元素
    """
    n = len(matrix)
    diagonal_sum = sum(matrix[i][i] for i in range(n))  # 对角线元素和
    above_diagonal_sum = sum(matrix[i][j] for i in range(n) for j in range(i+1, n))  # 上三角元素和
    return diagonal_sum + above_diagonal_sum


def compute_transition_matrix(pred):
    """
    根据状态序列计算概率转移矩阵

    参数:
        pred (array-like): 状态序列，包含离散的状态标签

    返回:
        np.ndarray: 概率转移矩阵，元素(i,j)表示从状态i转移到状态j的概率

    算法原理:
        1. 统计相邻时间步之间的状态转移次数
        2. 按行归一化得到转移概率
        3. 处理零除法情况

    应用:
        用于马尔科夫建模评价，比较高维和低维数据的动态特性
    """
    num_states = max(pred) + 1
    transition_counts = np.zeros((num_states, num_states))

    # 统计状态转移次数
    for current_state, next_state in zip(pred[:-1], pred[1:]):
        transition_counts[current_state, next_state] += 1

    # 计算转移概率（按行归一化）
    row_sums = transition_counts.sum(axis=1, keepdims=True)
    transition_matrix = np.divide(transition_counts, row_sums, where=row_sums!=0)
    return transition_matrix


def normalize_score(score, score_type='higher_better', min_val=None, max_val=None):
    """
    标准化评分到[0,1]范围

    参数:
        score (float or array): 原始评分
        score_type (str): 评分类型，'higher_better'或'lower_better'
        min_val (float): 最小值，用于标准化
        max_val (float): 最大值，用于标准化

    返回:
        float or array: 标准化后的评分
    """
    if isinstance(score, (list, np.ndarray)):
        score = np.array(score)
        if min_val is None:
            min_val = score.min()
        if max_val is None:
            max_val = score.max()
    else:
        if min_val is None:
            min_val = 0
        if max_val is None:
            max_val = 1

    # 避免除零
    if max_val == min_val:
        return 0.5

    # 标准化
    normalized = (score - min_val) / (max_val - min_val)

    # 如果是越小越好的指标，需要反转
    if score_type == 'lower_better':
        normalized = 1 - normalized

    return np.clip(normalized, 0, 1)


# ================================ 主评价类 ===================================

class DimensionalityReductionEvaluator:
    """
    降维方法综合评价器 (Enhanced Version)

    该类提供了全面的降维方法评价功能，包含七大评价维度：
    1. 重构质量评价
    2. 结构保持性评价
    3. 聚类性能评价
    4. 分类性能评价
    5. 稳定性和鲁棒性评价
    6. 计算效率评价
    7. 可解释性评价

    使用示例:
        evaluator = DimensionalityReductionEvaluator(n_cluster=15)
        results = evaluator.comprehensive_evaluate(X_original, X_reduced, X_reconstructed)
    """

    def __init__(self, n_cluster=15, max_components=10, component_step=1, random_state=42):
        """
        初始化评价器

        参数:
            n_cluster (int): 聚类数量，用于聚类性能评价
            max_components (int): 最大降维维数，用于多维度评价
            component_step (int): 维数步长
            random_state (int): 随机种子，确保结果可重现

        属性:
            evaluation_history (list): 存储历史评价结果
            weights (dict): 各评价维度的权重
        """
        self.n_cluster = n_cluster
        self.max_components = max_components
        self.component_step = component_step
        self.random_state = random_state
        self.evaluation_history = []

        # 默认权重配置
        self.weights = {
            'reconstruction_quality': 0.20,
            'structure_preservation': 0.25,
            'clustering_performance': 0.20,
            'classification_performance': 0.15,
            'stability': 0.10,
            'efficiency': 0.05,
            'interpretability': 0.05
        }

        # 设置随机种子
        np.random.seed(random_state)

    def _check_inverse_transform_support(self, method_name, reduction_method=None):
        """
        检测降维方法是否支持逆变换

        参数:
            method_name (str): 方法名称
            reduction_method: 方法对象

        返回:
            bool: 是否支持逆变换
        """
        # 基于方法名称的检测
        non_invertible_methods = {
            'TSNE', 't-SNE', 'tsne', 'T-SNE',
            'MDS', 'mds', 'MultidimensionalScaling',
            'Isomap', 'ISOMAP', 'isomap',
            'LLE', 'lle', 'LocallyLinearEmbedding',
            'UMAP', 'umap',
            'SpectralEmbedding', 'Laplacian',
            'DiffusionMaps'
        }

        if method_name in non_invertible_methods:
            return False

        # 基于方法对象的检测
        if reduction_method is not None:
            # 检查是否有inverse_transform方法
            if hasattr(reduction_method, 'inverse_transform'):
                return True

            # 检查方法类型
            method_type = type(reduction_method).__name__
            if method_type in non_invertible_methods:
                return False

        # 默认假设支持逆变换（如PCA、FastICA、NMF等）
        return True
        
    # ================================ 1. 重构质量评价 ===================================

    def evaluate_reconstruction_error(self, X_original, X_reconstructed, has_inverse_transform=True):
        """
        评价重构误差 - 衡量降维后重构数据与原始数据的差异

        参数:
            X_original (np.ndarray): 原始数据矩阵，形状为(n_samples, n_features)
            X_reconstructed (np.ndarray): 重构数据矩阵，形状与原始数据相同
            has_inverse_transform (bool): 是否支持逆变换，默认True

        返回:
            tuple: (frobenius_error, rmse_error, mae_error, relative_error)
            - frobenius_error: 相对Frobenius范数误差 [0, +∞)，越小越好
            - rmse_error: 均方根误差 [0, +∞)，越小越好
            - mae_error: 平均绝对误差 [0, +∞)，越小越好
            - relative_error: 相对误差 [0, +∞)，越小越好

        注意:
            - 对于不支持逆变换的方法（如t-SNE、MDS、Isomap、LLE），所有误差设为10000
            - 这是一个惩罚值，表示无法进行重构评价

        数学原理:
            - Frobenius范数: ||X_orig - X_recon||_F / ||X_orig||_F
            - RMSE: sqrt(MSE(X_orig, X_recon))
            - MAE: mean(|X_orig - X_recon|)
            - 相对误差: mean(|X_orig - X_recon| / (|X_orig| + ε))
        """
        # 检查是否支持逆变换
        if not has_inverse_transform:
            print("  警告: 该方法不支持逆变换，重构误差设为惩罚值 10000")
            return 10000.0, 10000.0, 10000.0, 10000.0

        # 检查重构数据是否有效
        if X_reconstructed is None:
            print("  警告: 重构数据为None，重构误差设为惩罚值 10000")
            return 10000.0, 10000.0, 10000.0, 10000.0

        # 检查数据形状是否匹配
        if X_original.shape != X_reconstructed.shape:
            print(f"  警告: 数据形状不匹配 {X_original.shape} vs {X_reconstructed.shape}，重构误差设为惩罚值 10000")
            return 10000.0, 10000.0, 10000.0, 10000.0

        try:
            # 计算误差矩阵
            error_matrix = X_original - X_reconstructed

            # 1. Frobenius范数误差（相对）
            frobenius_error = np.linalg.norm(error_matrix) / (np.linalg.norm(X_original) + 1e-10)

            # 2. RMSE误差
            mse = mean_squared_error(X_original, X_reconstructed)
            rmse_error = np.sqrt(mse)

            # 3. 平均绝对误差
            mae_error = np.mean(np.abs(error_matrix))

            # 4. 相对误差（避免除零）
            relative_error = np.mean(np.abs(error_matrix) / (np.abs(X_original) + 1e-10))

            return frobenius_error, rmse_error, mae_error, relative_error

        except Exception as e:
            print(f"重构误差计算出错: {e}，设为惩罚值 10000")
            return 10000.0, 10000.0, 10000.0, 10000.0
    
    def evaluate_structure_preservation(self, X_original, X_reduced):
        """
        评价结构保持性
        参数:
        X_original: 原始高维数据
        X_reduced: 降维后数据
        返回:
        correlation: 皮尔逊相关系数
        mutual_info: 互信息
        dtw_distance: DTW距离
        trustworthiness_score: 可信度分数
        """
        # 计算距离矩阵
        high_dim_distances = pairwise_distances(X_original, metric='euclidean')
        low_dim_distances = pairwise_distances(X_reduced, metric='euclidean')
        
        # 取上三角矩阵并计算均值
        upper_high = np.triu(high_dim_distances)
        upper_low = np.triu(low_dim_distances)
        high_dim_means = np.mean(upper_high, axis=1)
        low_dim_means = np.mean(upper_low, axis=1)
        
        # 标准化
        scaler = MinMaxScaler()
        dim_data = scaler.fit_transform(np.column_stack([low_dim_means, high_dim_means]))
        
        # 皮尔逊相关系数
        correlation, _ = stats.pearsonr(dim_data[:, 0], dim_data[:, 1])
        
        # 互信息
        mutual_info = mutual_info_score(dim_data[:, 0], dim_data[:, 1])
        
        # DTW距离
        def euclidean_distance(x, y):
            return np.sqrt(np.sum((x - y) ** 2))
        dtw_distance, _, _, _ = dtw([dim_data[:, 0]], [dim_data[:, 1]], dist=euclidean_distance)
        
        # 可信度评分
        trustworthiness_score = trustworthiness(X_original, X_reduced, n_neighbors=7, metric='euclidean')
        
        return correlation, mutual_info, dtw_distance, trustworthiness_score
    
    def evaluate_clustering_performance(self, X_original, X_reduced):
        """
        评价聚类性能
        参数:
        X_original: 原始数据
        X_reduced: 降维数据
        返回:
        rand_score: 调整兰德指数
        nmi_score: 标准化互信息
        fmi_score: Fowlkes-Mallows指数
        silhouette_score: 轮廓系数
        """
        # 高维聚类
        high_kmeans = Us_KMeans(self.n_cluster, max_iter=500, init='dense_sampling', handle_missing='assign_zero')
        high_kmeans.fit(X_original)
        high_labels = high_kmeans.labels_
        
        # 低维聚类
        low_kmeans = Us_KMeans(self.n_cluster, max_iter=500, init='dense_sampling', handle_missing='assign_zero')
        low_kmeans.fit(X_reduced)
        low_labels = low_kmeans.labels_
        
        # 聚类一致性评价
        rand_score = adjusted_rand_score(low_labels, high_labels)
        nmi_score = normalized_mutual_info_score(low_labels, high_labels)
        fmi_score = fowlkes_mallows_score(low_labels, high_labels)
        
        # 轮廓系数
        silhouette_score = metrics.silhouette_score(X_reduced, low_labels, metric='euclidean')
        
        return rand_score, nmi_score, fmi_score, silhouette_score
    
    def evaluate_classification_performance(self, X_original, X_reduced):
        """
        评价分类性能
        参数:
        X_original: 原始数据
        X_reduced: 降维数据
        返回:
        classification_score: 分类准确率
        """
        # 使用原始数据的聚类结果作为标签
        kmeans = Us_KMeans(self.n_cluster, max_iter=500, init='dense_sampling', handle_missing='assign_zero')
        kmeans.fit(X_original)
        labels = kmeans.labels_
        
        # 构建标记数据集
        labeled_dataset = np.column_stack([X_reduced, labels])
        np.random.shuffle(labeled_dataset)
        
        data = labeled_dataset[:, :-1]
        target = labeled_dataset[:, -1]
        
        # 数据划分
        X_train, X_test, y_train, y_test = train_test_split(data, target, test_size=0.6, random_state=42)
        
        # 网格搜索最优参数
        parameters = {'n_estimators': [5, 10, 15, 20, 25, 30], 'max_depth': [2, 4, 6, 8, 10, 12]}
        clf = GridSearchCV(RandomForestClassifier(), parameters, cv=5, scoring='accuracy', n_jobs=-1)
        clf.fit(X_train, y_train)
        
        return clf.best_score_
    
    def evaluate_markov_modeling(self, X_original, X_reduced):
        """
        评价马尔科夫建模性能
        参数:
        X_original: 原始数据
        X_reduced: 降维数据
        返回:
        transition_error: 转移矩阵误差
        """
        # 高维聚类
        high_kmeans = Us_KMeans(self.n_cluster, max_iter=500, init='dense_sampling', handle_missing='assign_zero')
        high_kmeans.fit(X_original)
        high_labels = high_kmeans.labels_
        
        # 低维聚类
        low_kmeans = Us_KMeans(self.n_cluster, max_iter=500, init='dense_sampling', handle_missing='assign_zero')
        low_kmeans.fit(X_reduced)
        low_labels = low_kmeans.labels_
        
        # 计算转移矩阵
        high_tpm = compute_transition_matrix(high_labels)
        low_tpm = compute_transition_matrix(low_labels)
        
        # 计算误差
        err_tpm = low_tpm - high_tpm
        transition_error = sum_above_diagonal(np.abs(err_tpm))
        
        return transition_error, high_tpm, low_tpm
    
    def evaluate_nonlinear_features(self, X_reduced):
        """
        评价非线性特征
        参数:
        X_reduced: 降维数据
        返回:
        sampen_mean: 样本熵均值
        hurst_mean: Hurst指数均值
        lyap_mean: 李雅普诺夫指数均值
        """
        n_components = X_reduced.shape[1]
        
        # 样本熵
        sampens = []
        for i in range(n_components):
            sampen = nolds.sampen(X_reduced[:, i], emb_dim=2, tolerance=None)
            sampens.append(sampen)
        sampen_mean = np.mean(sampens)
        
        # Hurst指数
        hursts = []
        for i in range(n_components):
            hurst = nolds.hurst_rs(X_reduced[:, i])
            hursts.append(hurst)
        hurst_mean = np.mean(hursts)
        
        # 李雅普诺夫指数
        lyaps = []
        for i in range(n_components):
            lyap = nolds.lyap_r(X_reduced[:, i])
            lyaps.append(lyap)
        lyap_mean = np.mean(lyaps)
        
        return sampen_mean, hurst_mean, lyap_mean
    
    def evaluate_linear_modeling(self, X_reduced, dims=[2, 3], degrees=[1, 2, 3]):
        """
        评价线性建模性能
        参数:
        X_reduced: 降维数据
        dims: 建模维度列表
        degrees: 多项式阶数列表
        返回:
        r2_scores: R²分数数组
        mse_scores: MSE分数数组
        """
        r2_scores = []
        mse_scores = []
        
        for dim in dims:
            for degree in degrees:
                if dim > X_reduced.shape[1]:
                    continue
                    
                # 选择前dim个维度
                data_subset = X_reduced[:, :dim]
                
                # 计算梯度作为因变量
                t = 0.01
                gradients = []
                for i in range(dim):
                    gradient = np.gradient(data_subset[:, i], t)
                    gradients.append(gradient)
                target = np.array(gradients).T
                
                # 数据划分
                X_train, X_test, y_train, y_test = train_test_split(
                    data_subset, target, train_size=0.8, random_state=40
                )
                
                # 标准化
                scaler_X = StandardScaler()
                X_train_scaled = scaler_X.fit_transform(X_train)
                X_test_scaled = scaler_X.transform(X_test)
                
                scaler_y = StandardScaler()
                y_train_scaled = scaler_y.fit_transform(y_train)
                y_test_scaled = scaler_y.transform(y_test)
                
                # 多项式特征
                poly = PolynomialFeatures(degree=degree)
                X_poly = poly.fit_transform(data_subset)[:, 1:]  # 去除常数项
                
                # 线性回归
                model = LinearRegression(fit_intercept=False)
                model.fit(X_poly, target)
                
                # 预测和评价
                y_pred = model.predict(X_poly)
                r2 = r2_score(target, y_pred)
                mse = mean_squared_error(target, y_pred)
                
                r2_scores.append(r2)
                mse_scores.append(mse)
        
        return np.array(r2_scores), np.array(mse_scores)

    # ================================ 5. 稳定性和鲁棒性评价 ===================================

    def evaluate_stability(self, X_original, reduction_method, n_components, n_runs=10, noise_level=0.01):
        """
        评价降维方法的稳定性和鲁棒性

        参数:
            X_original (np.ndarray): 原始数据
            reduction_method: 降维方法对象（需要有fit_transform方法）
            n_components (int): 降维维数
            n_runs (int): 运行次数，用于统计稳定性
            noise_level (float): 噪声水平，相对于数据标准差的比例

        返回:
            dict: 包含稳定性评价结果
            - stability_score: 稳定性分数 [0, 1]，越大越稳定
            - noise_robustness: 噪声鲁棒性 [0, 1]，越大越鲁棒
            - parameter_sensitivity: 参数敏感性 [0, 1]，越小越不敏感

        评价原理:
            1. 多次运行同一方法，计算结果的方差
            2. 添加不同水平的噪声，测试鲁棒性
            3. 改变参数设置，测试敏感性
        """
        try:
            results_clean = []
            results_noisy = []

            # 计算噪声标准差
            noise_std = noise_level * np.std(X_original)

            for run in range(n_runs):
                # 设置不同的随机种子
                if hasattr(reduction_method, 'random_state'):
                    reduction_method.random_state = self.random_state + run

                # 1. 干净数据的结果
                try:
                    X_reduced_clean = reduction_method.fit_transform(X_original)
                    results_clean.append(X_reduced_clean)
                except:
                    # 如果方法不支持重复运行，使用原始结果
                    if len(results_clean) == 0:
                        X_reduced_clean = reduction_method.fit_transform(X_original)
                        results_clean.append(X_reduced_clean)
                    else:
                        results_clean.append(results_clean[0])

                # 2. 加噪声数据的结果
                X_noisy = X_original + np.random.normal(0, noise_std, X_original.shape)
                try:
                    X_reduced_noisy = reduction_method.fit_transform(X_noisy)
                    results_noisy.append(X_reduced_noisy)
                except:
                    results_noisy.append(X_reduced_clean)

            # 计算稳定性指标
            results_clean = np.array(results_clean)
            results_noisy = np.array(results_noisy)

            # 1. 稳定性分数（基于方差）
            if len(results_clean) > 1:
                variance_per_point = np.var(results_clean, axis=0)
                mean_variance = np.mean(variance_per_point)
                stability_score = 1 / (1 + mean_variance)  # 转换为[0,1]，越大越稳定
            else:
                stability_score = 1.0

            # 2. 噪声鲁棒性
            if len(results_noisy) > 0 and len(results_clean) > 0:
                # 计算加噪声前后结果的相似性
                similarity_scores = []
                for i in range(min(len(results_clean), len(results_noisy))):
                    # 使用余弦相似度
                    clean_flat = results_clean[i].flatten()
                    noisy_flat = results_noisy[i].flatten()

                    # 避免零向量
                    if np.linalg.norm(clean_flat) > 1e-10 and np.linalg.norm(noisy_flat) > 1e-10:
                        similarity = np.dot(clean_flat, noisy_flat) / (
                            np.linalg.norm(clean_flat) * np.linalg.norm(noisy_flat)
                        )
                        similarity_scores.append(max(0, similarity))  # 确保非负

                noise_robustness = np.mean(similarity_scores) if similarity_scores else 0.5
            else:
                noise_robustness = 0.5

            # 3. 参数敏感性（简化版本）
            parameter_sensitivity = 1 - stability_score  # 稳定性的反面

            return {
                'stability_score': stability_score,
                'noise_robustness': noise_robustness,
                'parameter_sensitivity': parameter_sensitivity,
                'variance_details': np.mean(variance_per_point) if len(results_clean) > 1 else 0
            }

        except Exception as e:
            print(f"稳定性评价出错: {e}")
            return {
                'stability_score': 0.5,
                'noise_robustness': 0.5,
                'parameter_sensitivity': 0.5,
                'variance_details': 0
            }

    # ================================ 6. 计算效率评价 ===================================

    def evaluate_computational_efficiency(self, X_original, reduction_method, n_components):
        """
        评价计算效率

        参数:
            X_original (np.ndarray): 原始数据
            reduction_method: 降维方法对象
            n_components (int): 降维维数

        返回:
            dict: 效率评价结果
            - time_cost: 时间消耗（秒）
            - memory_usage: 内存使用（MB）
            - scalability: 可扩展性指标（时间/样本数）
            - efficiency_score: 综合效率分数 [0, 1]
        """
        try:
            # 获取当前进程
            process = psutil.Process()

            # 记录初始内存
            memory_before = process.memory_info().rss / 1024 / 1024  # MB

            # 记录开始时间
            start_time = time.time()

            # 执行降维
            X_reduced = reduction_method.fit_transform(X_original)

            # 记录结束时间
            end_time = time.time()

            # 记录结束内存
            memory_after = process.memory_info().rss / 1024 / 1024  # MB

            # 计算指标
            time_cost = end_time - start_time
            memory_usage = max(0, memory_after - memory_before)
            scalability = time_cost / X_original.shape[0] if X_original.shape[0] > 0 else 0

            # 计算效率分数（基于时间和内存的综合评价）
            # 使用对数变换，避免极值影响
            time_score = 1 / (1 + np.log10(max(time_cost, 0.001)))
            memory_score = 1 / (1 + np.log10(max(memory_usage, 0.1)))
            efficiency_score = (time_score + memory_score) / 2

            return {
                'time_cost': time_cost,
                'memory_usage': memory_usage,
                'scalability': scalability,
                'efficiency_score': efficiency_score,
                'time_score': time_score,
                'memory_score': memory_score
            }

        except Exception as e:
            print(f"效率评价出错: {e}")
            return {
                'time_cost': np.inf,
                'memory_usage': np.inf,
                'scalability': np.inf,
                'efficiency_score': 0.0,
                'time_score': 0.0,
                'memory_score': 0.0
            }

    # ================================ 7. 可解释性评价 ===================================

    def evaluate_interpretability(self, X_original, X_reduced, reduction_method):
        """
        评价降维结果的可解释性

        参数:
            X_original (np.ndarray): 原始数据
            X_reduced (np.ndarray): 降维后数据
            reduction_method: 降维方法对象

        返回:
            dict: 可解释性评价结果
            - explained_variance_ratio: 方差解释比（线性方法）
            - cumulative_variance: 累积方差解释比
            - interpretability_score: 可解释性综合分数 [0, 1]
            - feature_importance: 特征重要性（如果可用）
        """
        try:
            result = {
                'explained_variance_ratio': None,
                'cumulative_variance': None,
                'interpretability_score': 0.5,  # 默认中等可解释性
                'feature_importance': None,
                'method_type': 'unknown'
            }

            # 检查是否为线性方法（有components_属性）
            if hasattr(reduction_method, 'explained_variance_ratio_'):
                # PCA等线性方法
                explained_variance_ratio = reduction_method.explained_variance_ratio_
                cumulative_variance = np.cumsum(explained_variance_ratio)

                result.update({
                    'explained_variance_ratio': explained_variance_ratio,
                    'cumulative_variance': cumulative_variance,
                    'interpretability_score': np.sum(explained_variance_ratio),  # 总方差解释比
                    'method_type': 'linear'
                })

                # 如果有components_，提取特征重要性
                if hasattr(reduction_method, 'components_'):
                    # 计算每个原始特征的重要性（所有主成分的贡献之和）
                    feature_importance = np.sum(np.abs(reduction_method.components_), axis=0)
                    feature_importance = feature_importance / np.sum(feature_importance)  # 归一化
                    result['feature_importance'] = feature_importance

            elif hasattr(reduction_method, 'embedding_'):
                # 流形学习方法
                result.update({
                    'interpretability_score': 0.3,  # 流形学习可解释性较低
                    'method_type': 'manifold'
                })

            elif hasattr(reduction_method, 'components_'):
                # ICA等方法
                # 计算独立成分的可解释性
                components = reduction_method.components_
                # 使用成分的稀疏性作为可解释性指标
                sparsity = np.mean([np.sum(np.abs(comp) < 0.1 * np.max(np.abs(comp))) / len(comp)
                                   for comp in components])

                result.update({
                    'interpretability_score': sparsity,
                    'method_type': 'independent_component',
                    'feature_importance': np.sum(np.abs(components), axis=0)
                })

            else:
                # 非线性方法（t-SNE, UMAP等）
                # 使用局部结构保持性作为可解释性的代理指标
                if X_reduced.shape[1] <= 3:  # 低维可视化
                    result.update({
                        'interpretability_score': 0.8,  # 低维可视化可解释性较高
                        'method_type': 'nonlinear_visualization'
                    })
                else:
                    result.update({
                        'interpretability_score': 0.4,  # 高维非线性方法可解释性较低
                        'method_type': 'nonlinear_embedding'
                    })

            return result

        except Exception as e:
            print(f"可解释性评价出错: {e}")
            return {
                'explained_variance_ratio': None,
                'cumulative_variance': None,
                'interpretability_score': 0.5,
                'feature_importance': None,
                'method_type': 'unknown'
            }

    # ================================ 8. 多尺度结构评价 ===================================

    def evaluate_multiscale_structure(self, X_original, X_reduced):
        """
        评价多尺度结构保持性

        参数:
            X_original (np.ndarray): 原始高维数据
            X_reduced (np.ndarray): 降维后数据

        返回:
            dict: 多尺度结构评价结果
            - multiscale_scores: 不同尺度下的trustworthiness分数
            - average_preservation: 平均结构保持性
            - scale_consistency: 尺度一致性
        """
        try:
            # 定义不同的邻域尺度
            max_neighbors = min(50, X_original.shape[0] - 1)
            scales = [k for k in [5, 10, 20, 30, 50] if k < max_neighbors]

            if not scales:
                scales = [min(5, max_neighbors)]

            preservation_scores = []

            for k in scales:
                try:
                    score = trustworthiness(X_original, X_reduced, n_neighbors=k, metric='euclidean')
                    preservation_scores.append(score)
                except:
                    preservation_scores.append(0.5)  # 默认值

            # 计算统计指标
            average_preservation = np.mean(preservation_scores)
            scale_consistency = 1 - np.std(preservation_scores)  # 一致性：标准差越小越一致

            return {
                'scales': scales,
                'multiscale_scores': preservation_scores,
                'average_preservation': average_preservation,
                'scale_consistency': max(0, scale_consistency)  # 确保非负
            }

        except Exception as e:
            print(f"多尺度结构评价出错: {e}")
            return {
                'scales': [5],
                'multiscale_scores': [0.5],
                'average_preservation': 0.5,
                'scale_consistency': 0.5
            }

    # ================================ 9. 综合评价方法 ===================================

    def comprehensive_evaluate(self, X_original, X_reduced, X_reconstructed=None,
                              reduction_method=None, method_name="Unknown",
                              has_inverse_transform=None):
        """
        综合评价降维方法的所有指标

        参数:
            X_original (np.ndarray): 原始数据
            X_reduced (np.ndarray): 降维后数据
            X_reconstructed (np.ndarray, optional): 重构数据
            reduction_method (optional): 降维方法对象
            method_name (str): 方法名称
            has_inverse_transform (bool, optional): 是否支持逆变换，自动检测

        返回:
            dict: 综合评价结果
        """
        print(f"开始综合评价降维方法: {method_name}")

        # 自动检测是否支持逆变换
        if has_inverse_transform is None:
            has_inverse_transform = self._check_inverse_transform_support(method_name, reduction_method)

        results = {
            'method_name': method_name,
            'has_inverse_transform': has_inverse_transform,
            'data_info': {
                'n_samples': X_original.shape[0],
                'n_features_original': X_original.shape[1],
                'n_features_reduced': X_reduced.shape[1],
                'compression_ratio': X_reduced.shape[1] / X_original.shape[1]
            }
        }

        # 1. 重构质量评价
        print("  - 评价重构质量...")
        frobenius_error, rmse_error, mae_error, relative_error = self.evaluate_reconstruction_error(
            X_original, X_reconstructed, has_inverse_transform
        )
        results['reconstruction_quality'] = {
            'frobenius_error': frobenius_error,
            'rmse_error': rmse_error,
            'mae_error': mae_error,
            'relative_error': relative_error,
            'quality_score': normalize_score(frobenius_error, 'lower_better') if frobenius_error < 1000 else 0.0,
            'has_reconstruction': has_inverse_transform
        }
        # 2. 结构保持性评价
        print("  - 评价结构保持性...")
        correlation, mutual_info, dtw_distance, trustworthiness_score = self.evaluate_structure_preservation(
            X_original, X_reduced
        )
        results['structure_preservation'] = {
            'correlation': correlation,
            'mutual_info': mutual_info,
            'dtw_distance': dtw_distance,
            'trustworthiness': trustworthiness_score,
            'structure_score': (correlation + trustworthiness_score) / 2
        }

        # 3. 多尺度结构评价
        print("  - 评价多尺度结构...")
        multiscale_results = self.evaluate_multiscale_structure(X_original, X_reduced)
        results['multiscale_structure'] = multiscale_results

        # 4. 聚类性能评价
        print("  - 评价聚类性能...")
        try:
            rand_score, nmi_score, fmi_score, silhouette_score = self.evaluate_clustering_performance(
                X_original, X_reduced
            )
            results['clustering_performance'] = {
                'rand_score': rand_score,
                'nmi_score': nmi_score,
                'fmi_score': fmi_score,
                'silhouette_score': silhouette_score,
                'clustering_score': (rand_score + nmi_score + fmi_score) / 3
            }
        except Exception as e:
            print(f"    聚类评价出错: {e}")
            results['clustering_performance'] = {
                'rand_score': 0.5, 'nmi_score': 0.5, 'fmi_score': 0.5,
                'silhouette_score': 0.5, 'clustering_score': 0.5
            }

        # 5. 分类性能评价
        print("  - 评价分类性能...")
        try:
            classification_score = self.evaluate_classification_performance(X_original, X_reduced)
            results['classification_performance'] = {
                'accuracy': classification_score,
                'classification_score': classification_score
            }
        except Exception as e:
            print(f"    分类评价出错: {e}")
            results['classification_performance'] = {
                'accuracy': 0.5, 'classification_score': 0.5
            }

        # 6. 马尔科夫建模评价
        print("  - 评价马尔科夫建模...")
        try:
            transition_error, high_tpm, low_tpm = self.evaluate_markov_modeling(X_original, X_reduced)
            results['markov_modeling'] = {
                'transition_error': transition_error,
                'markov_score': normalize_score(transition_error, 'lower_better')
            }
        except Exception as e:
            print(f"    马尔科夫评价出错: {e}")
            results['markov_modeling'] = {
                'transition_error': 1.0, 'markov_score': 0.5
            }

        # 7. 非线性特征评价
        print("  - 评价非线性特征...")
        try:
            sampen_mean, hurst_mean, lyap_mean = self.evaluate_nonlinear_features(X_reduced)
            results['nonlinear_features'] = {
                'sampen_mean': sampen_mean,
                'hurst_mean': hurst_mean,
                'lyap_mean': lyap_mean,
                'nonlinear_score': (sampen_mean + abs(hurst_mean - 0.5) * 2) / 2
            }
        except Exception as e:
            print(f"    非线性特征评价出错: {e}")
            results['nonlinear_features'] = {
                'sampen_mean': 1.0, 'hurst_mean': 0.5, 'lyap_mean': 0.0, 'nonlinear_score': 0.5
            }

        # 8. 稳定性评价（如果提供了方法对象）
        if reduction_method is not None:
            print("  - 评价稳定性...")
            try:
                stability_results = self.evaluate_stability(
                    X_original, reduction_method, X_reduced.shape[1]
                )
                results['stability'] = stability_results
            except Exception as e:
                print(f"    稳定性评价出错: {e}")
                results['stability'] = {
                    'stability_score': 0.5, 'noise_robustness': 0.5,
                    'parameter_sensitivity': 0.5, 'variance_details': 0
                }

            # 9. 效率评价
            print("  - 评价计算效率...")
            try:
                efficiency_results = self.evaluate_computational_efficiency(
                    X_original, reduction_method, X_reduced.shape[1]
                )
                results['efficiency'] = efficiency_results
            except Exception as e:
                print(f"    效率评价出错: {e}")
                results['efficiency'] = {
                    'time_cost': np.inf, 'memory_usage': np.inf, 'scalability': np.inf,
                    'efficiency_score': 0.0, 'time_score': 0.0, 'memory_score': 0.0
                }

            # 10. 可解释性评价
            print("  - 评价可解释性...")
            try:
                interpretability_results = self.evaluate_interpretability(
                    X_original, X_reduced, reduction_method
                )
                results['interpretability'] = interpretability_results
            except Exception as e:
                print(f"    可解释性评价出错: {e}")
                results['interpretability'] = {
                    'interpretability_score': 0.5, 'method_type': 'unknown'
                }

        # 11. 计算综合分数
        print("  - 计算综合分数...")
        comprehensive_score, score_breakdown = self.compute_comprehensive_score(results)
        results['comprehensive_evaluation'] = {
            'overall_score': comprehensive_score,
            'score_breakdown': score_breakdown,
            'weights_used': self.weights.copy()
        }

        # 保存到历史记录
        self.evaluation_history.append(results)

        print(f"评价完成! 综合分数: {comprehensive_score:.4f}")
        return results

    def compute_comprehensive_score(self, results):
        """
        计算综合评分

        参数:
            results (dict): 评价结果字典

        返回:
            tuple: (综合分数, 分数详情)
        """
        score_breakdown = {}
        total_score = 0
        total_weight = 0

        # 1. 重构质量 (20%)
        if 'reconstruction_quality' in results:
            quality_score = results['reconstruction_quality'].get('quality_score', 0.5)
            weight = self.weights['reconstruction_quality']
            score_breakdown['reconstruction_quality'] = quality_score * weight
            total_score += score_breakdown['reconstruction_quality']
            total_weight += weight

        # 2. 结构保持性 (25%)
        if 'structure_preservation' in results:
            structure_score = results['structure_preservation'].get('structure_score', 0.5)
            weight = self.weights['structure_preservation']
            score_breakdown['structure_preservation'] = structure_score * weight
            total_score += score_breakdown['structure_preservation']
            total_weight += weight

        # 3. 聚类性能 (20%)
        if 'clustering_performance' in results:
            clustering_score = results['clustering_performance'].get('clustering_score', 0.5)
            weight = self.weights['clustering_performance']
            score_breakdown['clustering_performance'] = clustering_score * weight
            total_score += score_breakdown['clustering_performance']
            total_weight += weight

        # 4. 分类性能 (15%)
        if 'classification_performance' in results:
            classification_score = results['classification_performance'].get('classification_score', 0.5)
            weight = self.weights['classification_performance']
            score_breakdown['classification_performance'] = classification_score * weight
            total_score += score_breakdown['classification_performance']
            total_weight += weight

        # 5. 稳定性 (10%)
        if 'stability' in results:
            stability_score = results['stability'].get('stability_score', 0.5)
            weight = self.weights['stability']
            score_breakdown['stability'] = stability_score * weight
            total_score += score_breakdown['stability']
            total_weight += weight

        # 6. 效率 (5%)
        if 'efficiency' in results:
            efficiency_score = results['efficiency'].get('efficiency_score', 0.5)
            weight = self.weights['efficiency']
            score_breakdown['efficiency'] = efficiency_score * weight
            total_score += score_breakdown['efficiency']
            total_weight += weight

        # 7. 可解释性 (5%)
        if 'interpretability' in results:
            interpretability_score = results['interpretability'].get('interpretability_score', 0.5)
            weight = self.weights['interpretability']
            score_breakdown['interpretability'] = interpretability_score * weight
            total_score += score_breakdown['interpretability']
            total_weight += weight

        # 标准化总分
        if total_weight > 0:
            comprehensive_score = total_score / total_weight
        else:
            comprehensive_score = 0.5

        return comprehensive_score, score_breakdown

    def statistical_significance_test(self, results_list, alpha=0.05):
        """
        对多个方法的评价结果进行统计显著性检验

        参数:
            results_list (list): 多个方法的评价结果列表
            alpha (float): 显著性水平

        返回:
            dict: 统计检验结果
        """
        if len(results_list) < 2:
            return {"error": "需要至少2个方法进行比较"}

        try:
            # 提取各方法的综合分数
            method_names = [result['method_name'] for result in results_list]
            overall_scores = [result['comprehensive_evaluation']['overall_score']
                            for result in results_list]

            # 提取各项指标分数
            metrics = ['reconstruction_quality', 'structure_preservation',
                      'clustering_performance', 'classification_performance']

            significance_results = {}

            # 对每个指标进行检验
            for metric in metrics:
                metric_scores = []
                for result in results_list:
                    if metric in result:
                        if metric == 'reconstruction_quality':
                            score = result[metric].get('quality_score', 0.5)
                        elif metric == 'structure_preservation':
                            score = result[metric].get('structure_score', 0.5)
                        elif metric == 'clustering_performance':
                            score = result[metric].get('clustering_score', 0.5)
                        elif metric == 'classification_performance':
                            score = result[metric].get('classification_score', 0.5)
                        else:
                            score = 0.5
                        metric_scores.append(score)
                    else:
                        metric_scores.append(0.5)

                # 如果有足够的数据点，进行统计检验
                if len(set(metric_scores)) > 1:  # 确保有变异性
                    try:
                        # 使用Kruskal-Wallis检验（非参数）
                        from scipy.stats import kruskal
                        statistic, p_value = kruskal(*[[score] for score in metric_scores])

                        significance_results[metric] = {
                            'test': 'Kruskal-Wallis',
                            'statistic': statistic,
                            'p_value': p_value,
                            'significant': p_value < alpha,
                            'scores': metric_scores,
                            'methods': method_names
                        }
                    except:
                        significance_results[metric] = {
                            'test': 'Failed',
                            'error': 'Statistical test failed'
                        }

            # 整体分数比较
            if len(set(overall_scores)) > 1:
                try:
                    from scipy.stats import kruskal
                    statistic, p_value = kruskal(*[[score] for score in overall_scores])

                    significance_results['overall'] = {
                        'test': 'Kruskal-Wallis',
                        'statistic': statistic,
                        'p_value': p_value,
                        'significant': p_value < alpha,
                        'scores': overall_scores,
                        'methods': method_names,
                        'best_method': method_names[np.argmax(overall_scores)],
                        'worst_method': method_names[np.argmin(overall_scores)]
                    }
                except:
                    significance_results['overall'] = {
                        'test': 'Failed',
                        'error': 'Overall statistical test failed'
                    }

            return significance_results

        except Exception as e:
            return {"error": f"统计检验出错: {e}"}

    def set_weights(self, new_weights):
        """
        设置评价权重

        参数:
            new_weights (dict): 新的权重字典
        """
        # 验证权重
        total_weight = sum(new_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            print(f"警告: 权重总和为 {total_weight:.3f}，建议调整为1.0")

        self.weights.update(new_weights)
        print("权重已更新:", self.weights)

    def get_evaluation_summary(self):
        """
        获取评价历史的摘要

        返回:
            dict: 评价摘要
        """
        if not self.evaluation_history:
            return {"message": "暂无评价历史"}

        summary = {
            'total_evaluations': len(self.evaluation_history),
            'methods_evaluated': [result['method_name'] for result in self.evaluation_history],
            'best_method': None,
            'worst_method': None,
            'average_scores': {}
        }

        # 找出最佳和最差方法
        scores = [result['comprehensive_evaluation']['overall_score']
                 for result in self.evaluation_history]

        if scores:
            best_idx = np.argmax(scores)
            worst_idx = np.argmin(scores)

            summary['best_method'] = {
                'name': self.evaluation_history[best_idx]['method_name'],
                'score': scores[best_idx]
            }
            summary['worst_method'] = {
                'name': self.evaluation_history[worst_idx]['method_name'],
                'score': scores[worst_idx]
            }
            summary['average_score'] = np.mean(scores)
            summary['score_std'] = np.std(scores)

        return summary
